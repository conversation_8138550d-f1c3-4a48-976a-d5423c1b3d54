# -*- coding: utf-8 -*-
"""
基于速度障碍法的避碰决策器
"""
import numpy as np
from typing import Dict, List, Tuple
from ..config import COLLISION_AVOIDANCE_CONFIG

class VelocityObstacle:
    """速度障碍避让规划器"""

    def __init__(self, safety_distance: float = 500.0):
        """
        初始化速度障碍避让规划器

        Args:
            safety_distance: 安全距离（米）
        """
        self.safety_distance = safety_distance

        # 速度约束
        self.max_speed = 7.2  # 最大速度（米/秒，约14节）
        self.min_speed = 2.0  # 最小速度（米/秒）

        # 转向约束
        self.max_heading_change = np.deg2rad(30.0)  # 最大转向角度（弧度）

    def calculate_velocity_obstacle(self, own_ship: Dict, target_ship: Dict) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        计算速度障碍区域

        Args:
            own_ship: 本船状态字典
            target_ship: 目标船状态字典

        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]:
                - vo_apex: 速度锥形区域的顶点坐标
                - left_bound: 左边界向量
                - right_bound: 右边界向量
        """
        # 提取位置信息
        own_pos = np.array([own_ship['x'], own_ship['y']])
        target_pos = np.array([target_ship['x'], target_ship['y']])

        # 计算相对位置和距离
        relative_pos = target_pos - own_pos
        distance = np.linalg.norm(relative_pos)

        # 安全检查，避免除以零
        if distance < 1e-6:
            # 如果距离太近，返回一个默认的速度障碍
            return np.array([0, 0]), np.array([1, 0]), np.array([0, 1])

        # 计算目标船的安全半径（考虑船舶尺寸）
        target_length = target_ship.get('length', 100.0)
        target_width = target_ship.get('width', 20.0)
        target_radius = max(target_length, target_width) / 2 + self.safety_distance

        # 计算锥形区域的顶角
        # 添加安全检查，确保arcsin的参数在[-1, 1]范围内
        sin_value = min(1.0, target_radius / distance)
        apex_angle = 2 * np.arcsin(sin_value)

        # 计算速度锥形区域的顶点（apex）
        # 顶点应该是目标船的速度向量
        target_vel = np.array([
            target_ship['u'] * np.cos(target_ship['psi']),
            target_ship['u'] * np.sin(target_ship['psi'])
        ])
        vo_apex = target_vel

        # 计算主方向向量（从本船指向目标船）
        direction = relative_pos / distance

        # 计算左右边界向量
        rotation_matrix_left = np.array([
            [np.cos(apex_angle/2), -np.sin(apex_angle/2)],
            [np.sin(apex_angle/2), np.cos(apex_angle/2)]
        ])
        rotation_matrix_right = np.array([
            [np.cos(-apex_angle/2), -np.sin(-apex_angle/2)],
            [np.sin(-apex_angle/2), np.cos(-apex_angle/2)]
        ])

        left_bound = rotation_matrix_left @ direction
        right_bound = rotation_matrix_right @ direction

        # 调试信息
        # print(f"VO计算 - 距离: {distance:.1f}m, 顶角: {np.rad2deg(apex_angle):.1f}°")
        # print(f"目标船速度: ({target_vel[0]:.2f}, {target_vel[1]:.2f}) m/s")
        # print(f"左边界方向: ({left_bound[0]:.2f}, {left_bound[1]:.2f})")
        # print(f"右边界方向: ({right_bound[0]:.2f}, {right_bound[1]:.2f})")

        return vo_apex, left_bound, right_bound

    def find_safe_velocity(self, own_ship: Dict, target_ships: List[Dict]) -> Tuple[float, float]:
        """
        寻找安全速度

        Args:
            own_ship: 本船状态字典
            target_ships: 目标船状态字典列表

        Returns:
            Tuple[float, float]: 安全速度（米/秒）和航向（度）
        """
        if not target_ships:
            return own_ship['u'], np.rad2deg(own_ship['psi'])

        # 当前速度向量
        current_speed = own_ship['u']
        current_heading = own_ship['psi']  # 已经是弧度单位，不需要转换
        current_vel = np.array([
            current_speed * np.cos(current_heading),
            current_speed * np.sin(current_heading)
        ])

        # 计算所有目标船的速度障碍
        all_obstacles = []
        for target_ship in target_ships:
            vo_apex, left_bound, right_bound = self.calculate_velocity_obstacle(
                own_ship, target_ship)
            all_obstacles.append((vo_apex, left_bound, right_bound))

        # 生成候选速度
        best_vel = current_vel
        min_cost = float('inf')

        # 在速度空间中搜索安全速度
        for speed in np.linspace(self.min_speed, self.max_speed, 10):
            for heading in np.linspace(
                current_heading - self.max_heading_change,
                current_heading + self.max_heading_change,
                12
            ):
                # 计算候选速度向量
                vel = np.array([
                    speed * np.cos(heading),
                    speed * np.sin(heading)
                ])

                # 检查是否在任何速度障碍区域内
                is_safe = True
                for vo_apex, left_bound, right_bound in all_obstacles:
                    if self._is_in_velocity_obstacle(vel, vo_apex, left_bound, right_bound):
                        is_safe = False
                        break

                if is_safe:
                    # 计算代价（与当前速度的偏差）
                    cost = np.linalg.norm(vel - current_vel)
                    if cost < min_cost:
                        min_cost = cost
                        best_vel = vel

        # 如果没有找到安全速度，进行紧急避让
        if min_cost == float('inf'):
            return self._emergency_maneuver(own_ship, target_ships)

        # 转换回速度和航向
        safe_speed = np.linalg.norm(best_vel)
        safe_heading = np.rad2deg(np.arctan2(best_vel[1], best_vel[0])) % 360

        return safe_speed, safe_heading

    def _is_in_velocity_obstacle(self, vel: np.ndarray, vo_apex: np.ndarray,
                               left_bound: np.ndarray, right_bound: np.ndarray) -> bool:
        """
        判断速度是否在速度障碍区域内

        Args:
            vel: 速度向量
            vo_apex: 速度障碍顶点
            left_bound: 左边界向量
            right_bound: 右边界向量

        Returns:
            bool: 是否在速度障碍区域内
        """
        # 计算相对速度向量
        rel_vel = vel - vo_apex

        # 检查是否在边界内
        cross_left = np.cross(left_bound, rel_vel)
        cross_right = np.cross(right_bound, rel_vel)

        return cross_left >= 0 and cross_right <= 0

    def _emergency_maneuver(self, own_ship: Dict, target_ships: List[Dict]) -> Tuple[float, float]:
        """
        紧急避让 - 严格遵守国际海上避碰规则

        Args:
            own_ship: 本船状态字典
            target_ships: 目标船状态字典列表

        Returns:
            Tuple[float, float]: 紧急避让的速度和航向
        """
        # 找到最近的目标船
        min_distance = float('inf')
        nearest_target = None

        for target_ship in target_ships:
            dx = target_ship['x'] - own_ship['x']
            dy = target_ship['y'] - own_ship['y']
            distance = np.sqrt(dx*dx + dy*dy)

            if distance < min_distance:
                min_distance = distance
                nearest_target = target_ship

        if nearest_target is None:
            return own_ship['u'], np.rad2deg(own_ship['psi'])

        # 导入风险评估器以获取会遇类型判断功能
        from ..collision_risk.risk_assessment import CollisionRiskAssessment
        risk_assessor = CollisionRiskAssessment()

        # 获取会遇类型和相对位置信息
        encounter_situation = risk_assessor.calculate_encounter_situation(own_ship, nearest_target)
        encounter_type = encounter_situation['type']
        is_give_way = encounter_situation['is_give_way']
        target_on_starboard = encounter_situation['target_on_starboard']
        relative_bearing_deg = encounter_situation['relative_bearing']

        # 计算当前航向（度）
        current_heading = np.rad2deg(own_ship['psi']) % 360

        # 根据国际海上避碰规则确定避让方向
        if encounter_type == 'head_on':
            # 对遇局面：向右转向避让（国际海上避碰规则第14条）
            new_heading = (current_heading + 60) % 360  # 向右转60度

        elif encounter_type == 'crossing':
            if target_on_starboard:
                # 交叉相遇，目标船在右舷：本船为让路船，应向右转向避让（国际海上避碰规则第15条）
                new_heading = (current_heading + 60) % 360  # 向右转60度
            else:
                # 交叉相遇，目标船在左舷：本船为直航船，应保持航向和航速
                # 但在紧急情况下，仍需采取避让行动（国际海上避碰规则第17条）
                # 此时可以选择减速或小幅度转向
                if min_distance < self.safety_distance * 0.7:
                    new_heading = (current_heading + 30) % 360  # 小幅向右转30度
                else:
                    new_heading = current_heading  # 保持航向

        elif encounter_type == 'overtaking':
            # 追越局面：追越船应避让被追越船（国际海上避碰规则第13条）
            # 可以选择从被追越船的任一侧通过
            # 通常选择从右侧通过更为安全
            new_heading = (current_heading + 30) % 360  # 向右转30度

        else:
            # 未知会遇类型，默认向右转向避让
            new_heading = (current_heading + 45) % 360  # 向右转45度

        # 根据距离调整速度
        if min_distance < self.safety_distance * 0.5:  # 距离非常近
            new_speed = max(self.min_speed, own_ship['u'] * 0.5)  # 减速50%
        elif min_distance < self.safety_distance:
            new_speed = max(self.min_speed, own_ship['u'] * 0.7)  # 减速30%
        else:
            new_speed = max(self.min_speed, own_ship['u'] * 0.9)  # 轻微减速10%

        return new_speed, new_heading