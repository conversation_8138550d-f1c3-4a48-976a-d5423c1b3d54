# -*- coding: utf-8 -*-
"""
避碰场景配置
"""
from typing import Dict, List

# 基本会遇场景
CROSSING_SCENARIO = {
    'name': 'crossing_encounter',
    'description': '交叉相遇场景',
    'own_ship': {
        'initial_state': {
            'x': 0.0,
            'y': 0.0,
            'psi': 0.0,  # 艏向角（弧度）
            'u': 7.0,    # 初始速度（m/s）
            'v': 0.0,
            'r': 0.0
        },
        'target_state': {
            'x': 5000.0,
            'y': 0.0,
            'psi': 0.0
        }
    },
    'target_ships': [
        {
            'id': 'Target1',
            'type': 'cargo',
            'length': 150.0,
            'width': 20.0,
            'initial_state': {
                'x': 2000.0,
                'y': 1000.0,
                'psi': -0.785,  # -45度（弧度）
                'u': 5.0,
                'v': 0.0,
                'r': 0.0
            }
        }
    ],
    'simulation_config': {
        'duration': 1800.0,  # 仿真时长（秒）
        'time_step': 1,    # 时间步长（秒）
        'safe_distance': 500.0  # 安全距离（米）
    }
}

HEAD_ON_SCENARIO = {
    'name': 'head_on_encounter',
    'description': '对遇场景',
    'own_ship': {
        'initial_state': {
            'x': 0.0,
            'y': 0.0,
            'psi': 0.0,
            'u': 7.0,
            'v': 0.0,
            'r': 0.0
        },
        'target_state': {
            'x': 5000.0,
            'y': 0.0,
            'psi': 0.0
        }
    },
    'target_ships': [
        {
            'id': 'Target1',
            'type': 'cargo',
            'length': 150.0,
            'width': 20.0,
            'initial_state': {
                'x': 4000.0,
                'y': 100.0,
                'psi': 3.14159,  # 180度（弧度）
                'u': 6.0,
                'v': 0.0,
                'r': 0.0
            }
        }
    ],
    'simulation_config': {
        'duration': 500,
        'time_step': 1,
        'safe_distance': 500.0
    }
}

OVERTAKING_SCENARIO = {
    'name': 'overtaking_encounter',
    'description': '追越场景',
    'own_ship': {
        'initial_state': {
            'x': 0.0,
            'y': 0.0,
            'psi': 0.0,
            'u': 8.0,
            'v': 0.0,
            'r': 0.0
        },
        'target_state': {
            'x': 5000.0,
            'y': 0.0,
            'psi': 0.0
        }
    },
    'target_ships': [
        {
            'id': 'Target1',
            'type': 'cargo',
            'length': 150.0,
            'width': 20.0,
            'initial_state': {
                'x': 1000.0,
                'y': 50.0,
                'psi': 0.0,
                'u': 5.0,
                'v': 0.0,
                'r': 0.0
            }
        }
    ],
    'simulation_config': {
        'duration': 1500.0,
        'time_step': 0.1,
        'safe_distance': 500.0
    }
}

MULTI_SHIP_SCENARIO = {
    'name': 'multi_ship_encounter',
    'description': '多船会遇场景',
    'own_ship': {
        'initial_state': {
            'x': 0.0,
            'y': 0.0,
            'psi': 0.0,
            'u': 7.0,
            'v': 0.0,
            'r': 0.0
        },
        'target_state': {
            'x': 5000.0,
            'y': 0.0,
            'psi': 0.0
        }
    },
    'target_ships': [
        {
            'id': 'Target1',
            'type': 'cargo',
            'length': 150.0,
            'width': 20.0,
            'initial_state': {
                'x': 2000.0,
                'y': 1000.0,
                'psi': -0.785,
                'u': 5.0,
                'v': 0.0,
                'r': 0.0
            }
        },
        {
            'id': 'Target2',
            'type': 'tanker',
            'length': 180.0,
            'width': 25.0,
            'initial_state': {
                'x': 3000.0,
                'y': -800.0,
                'psi': 0.523,  # 30度（弧度）
                'u': 4.5,
                'v': 0.0,
                'r': 0.0
            }
        },
        {
            'id': 'Target3',
            'type': 'cargo',
            'length': 120.0,
            'width': 18.0,
            'initial_state': {
                'x': 4000.0,
                'y': 200.0,
                'psi': 3.14159,
                'u': 6.0,
                'v': 0.0,
                'r': 0.0
            }
        }
    ],
    'simulation_config': {
        'duration': 2400.0,
        'time_step': 0.1,
        'safe_distance': 500.0
    }
}

# 极限工况测试场景
EXTREME_SCENARIO = {
    'name': 'extreme_condition',
    'description': '极限工况测试',
    'own_ship': {
        'initial_state': {
            'x': 0.0,
            'y': 0.0,
            'psi': 0.0,
            'u': 8.0,
            'v': 0.0,
            'r': 0.0
        },
        'target_state': {
            'x': 5000.0,
            'y': 0.0,
            'psi': 0.0
        }
    },
    'target_ships': [
        {
            'id': 'Target1',
            'type': 'cargo',
            'length': 150.0,
            'width': 20.0,
            'initial_state': {
                'x': 1500.0,
                'y': 400.0,
                'psi': -0.523,
                'u': 7.0,
                'v': 0.0,
                'r': 0.0
            }
        },
        {
            'id': 'Target2',
            'type': 'tanker',
            'length': 180.0,
            'width': 25.0,
            'initial_state': {
                'x': 2000.0,
                'y': -300.0,
                'psi': 0.262,
                'u': 6.5,
                'v': 0.0,
                'r': 0.0
            }
        }
    ],
    'simulation_config': {
        'duration': 1800.0,
        'time_step': 0.1,
        'safe_distance': 400.0,
        'environmental_conditions': {
            'wind_speed': 15.0,  # m/s
            'wind_direction': 45.0,  # 度
            'current_speed': 1.0,  # m/s
            'current_direction': 90.0  # 度
        }
    }
}

# 场景列表
SCENARIO_LIST = [
    CROSSING_SCENARIO,
    HEAD_ON_SCENARIO,
    OVERTAKING_SCENARIO,
    MULTI_SHIP_SCENARIO,
    EXTREME_SCENARIO
]

def get_scenario_by_name(name: str) -> Dict:
    """根据名称获取场景配置
    
    Args:
        name (str): 场景名称
        
    Returns:
        Dict: 场景配置
    """
    for scenario in SCENARIO_LIST:
        if scenario['name'] == name:
            return scenario
    raise ValueError(f"Unknown scenario name: {name}")

def list_scenarios() -> List[str]:
    """获取所有可用场景名称
    
    Returns:
        List[str]: 场景名称列表
    """
    return [scenario['name'] for scenario in SCENARIO_LIST]