# -*- coding: utf-8 -*-
"""
MMG模型水动力系数计算模块
"""
import numpy as np
from typing import Dict

class MMGCoefficients:
    def __init__(self, ship_params: Dict):
        """初始化MMG模型系数计算器
        
        Args:
            ship_params (Dict): 船舶参数
        """
        # 基本参数初始化
        self.L = ship_params['L']  # 船长
        self.B = ship_params['B']  # 船宽
        self.T = ship_params['T']  # 吃水
        self.Cb = ship_params['Cb']  # 方形系数
        
        # 计算船舶主尺度相关参数
        self.volume = self.L * self.B * self.T * self.Cb  # 排水体积
        self.mass = 1025 * self.volume  # 质量(kg)
        
        # 初始化系数
        self._init_hull_coefficients()
        self._init_propeller_coefficients()
        self._init_rudder_coefficients()
        
    def _init_hull_coefficients(self):
        """初始化船体系数"""
        # 船体运动导数系数（基于回归公式）
        self.Xvv = -0.04 * self.mass
        self.Xvr = 0.002 * self.mass
        self.Xrr = 0.011 * self.mass
        
        self.Yv = -0.315 * self.mass
        self.Yr = 0.083 * self.mass
        self.Yv_dot = -0.145 * self.mass
        self.Yr_dot = -0.0705 * self.mass
        
        self.Nv = -0.137 * self.mass * self.L
        self.Nr = -0.049 * self.mass * self.L
        self.Nv_dot = -0.0705 * self.mass * self.L
        self.Nr_dot = -0.0307 * self.mass * self.L * self.L
        
    def _init_propeller_coefficients(self):
        """初始化螺旋桨系数"""
        # 螺旋桨直径与节距比
        self.D_prop = 0.7 * self.T
        self.pitch_ratio = 0.8
        
        # 推力减额系数
        self.w_P0 = 0.4 * self.Cb
        self.t_P = 0.1
        
        # KT曲线系数
        self.kt_0 = 0.2885
        self.kt_1 = -0.2620
        self.kt_2 = -0.2313
        
    def _init_rudder_coefficients(self):
        """初始化舵系数"""
        # 舵面积和高宽比
        self.A_R = 0.02 * self.L * self.T
        self.lambda_R = 2.0
        
        # 舵力系数
        self.f_alpha = 2.747
        self.epsilon = 0.921
        self.kappa = 0.631
        
        # 舵位置系数
        self.t_R = 0.387
        self.x_R = -0.5 * self.L
        self.a_H = 0.312
        self.x_H = -0.464 * self.L
        
    def calculate_hull_forces(self, u: float, v: float, r: float) -> Dict:
        """计算船体力和力矩
        
        Args:
            u (float): 纵向速度
            v (float): 横向速度
            r (float): 角速度
            
        Returns:
            Dict: 船体力和力矩
        """
        # 计算速度平方项
        U = np.sqrt(u*u + v*v)
        beta = np.arctan2(-v, u)
        
        # 无因次化
        v_prime = v / U if U > 0.1 else 0
        r_prime = r * self.L / U if U > 0.1 else 0
        
        # 计算船体力
        X_H = (-self.Xvv * v_prime * abs(v_prime) * U * U -
               self.Xvr * v_prime * r_prime * U * U -
               self.Xrr * r_prime * abs(r_prime) * U * U)
        
        Y_H = (self.Yv * v_prime * U * U +
               self.Yr * r_prime * U * U +
               self.Yv_dot * U * beta +
               self.Yr_dot * U * r_prime)
        
        N_H = (self.Nv * v_prime * U * U +
               self.Nr * r_prime * U * U +
               self.Nv_dot * U * beta +
               self.Nr_dot * U * r_prime)
        
        return {
            'X_H': X_H,
            'Y_H': Y_H,
            'N_H': N_H
        }
    
    def calculate_propeller_forces(self, u: float, n_p: float) -> Dict:
        """计算螺旋桨力
        
        Args:
            u (float): 纵向速度
            n_p (float): 螺旋桨转速
            
        Returns:
            Dict: 螺旋桨力
        """
        # 计算进速系数
        J = u * (1 - self.w_P0) / (n_p * self.D_prop) if n_p > 0 else 0
        
        # 计算推力系数
        KT = self.kt_0 + self.kt_1 * J + self.kt_2 * J * J
        
        # 计算推力
        T = KT * 1025 * n_p * n_p * self.D_prop**4
        X_P = (1 - self.t_P) * T
        
        return {
            'X_P': X_P,
            'Y_P': 0.0,
            'N_P': 0.0
        }
    
    def calculate_rudder_forces(self, u: float, v: float, r: float, 
                              delta: float, n_p: float) -> Dict:
        """计算舵力和力矩
        
        Args:
            u (float): 纵向速度
            v (float): 横向速度
            r (float): 角速度
            delta (float): 舵角
            n_p (float): 螺旋桨转速
            
        Returns:
            Dict: 舵力和力矩
        """
        # 计算舵入流速度
        beta_R = beta = np.arctan2(-v, u)
        gamma_R = 0.395
        u_R = u * (1 - self.w_P0) * np.sqrt(1 + 
              gamma_R * (np.sqrt(1 + 8 * self.kappa * KT/(np.pi * self.J**2)) - 1))**2
        v_R = U * gamma_R * beta_R
        
        # 计算有效入流角
        alpha_R = delta - np.arctan2(v_R, u_R)
        
        # 计算舵法向力
        F_N = 0.5 * 1025 * self.A_R * self.f_alpha * (u_R**2 + v_R**2) * np.sin(alpha_R)
        
        # 计算舵力和力矩
        X_R = -(1 - self.t_R) * F_N * np.sin(delta)
        Y_R = -(1 + self.a_H) * F_N * np.cos(delta)
        N_R = -(self.x_R + self.a_H * self.x_H) * F_N * np.cos(delta)
        
        return {
            'X_R': X_R,
            'Y_R': Y_R,
            'N_R': N_R
        }
    
    def calculate_total_forces(self, state: Dict, controls: Dict) -> Dict:
        """计算总的力和力矩
        
        Args:
            state (Dict): 船舶状态
            controls (Dict): 控制输入
            
        Returns:
            Dict: 总的力和力矩
        """
        # 获取状态和控制量
        u = state['u']
        v = state['v']
        r = state['r']
        delta = controls['rudder_angle']
        n_p = controls['propeller_rps']
        
        # 计算各部分力
        hull_forces = self.calculate_hull_forces(u, v, r)
        prop_forces = self.calculate_propeller_forces(u, n_p)
        rudder_forces = self.calculate_rudder_forces(u, v, r, delta, n_p)
        
        # 合成总力和力矩
        X = hull_forces['X_H'] + prop_forces['X_P'] + rudder_forces['X_R']
        Y = hull_forces['Y_H'] + prop_forces['Y_P'] + rudder_forces['Y_R']
        N = hull_forces['N_H'] + prop_forces['N_P'] + rudder_forces['N_R']
        
        return {
            'X': X,
            'Y': Y,
            'N': N
        }

    def update_state(self, X, Y, N, dt):
        # 计算加速度
        u_dot = (X - self.mass * self.v * self.r) / self.mass
        v_dot = (Y - self.mass * self.u * self.r) / self.mass
        r_dot = N / self.Izz
        
        # 更新速度
        self.u += u_dot * dt
        self.v += v_dot * dt
        self.r += r_dot * dt
        
        # 更新位置和艏向
        self.x += (self.u * np.cos(self.psi) - self.v * np.sin(self.psi)) * dt
        self.y += (self.u * np.sin(self.psi) + self.v * np.cos(self.psi)) * dt
        self.psi += self.r * dt