# -*- coding: utf-8 -*-
"""
配置文件：定义系统参数和常量
"""

# 船舶参数
SHIP_CONFIG = {
    'L': 175.0,    # 船长(m)
    'B': 25.4,     # 船宽(m)
    'T': 8.0,      # 吃水(m)
    'Cb': 0.65,    # 方形系数

    # MMG模型参数
    'mass': 21222.0,  # 质量(ton)
    'Izz': 5.0e7,    # 转动惯量 - 极大增加以确保旋回时间约为400秒
    'xG': 0.0,       # 重心位置
}

# 控制器参数 - 根据新的船舶模型彻底重新调整
CONTROL_CONFIG = {
    'heading_pid': {
        'Kp': 10.0,   # 大幅增加比例增益，补偿船舶转向阻力增加和转动惯量增加
        'Ki': 0.5,    # 增加积分增益，确保能够消除稳态误差
        'Kd': 5.0,    # 增加微分增益，提高稳定性和响应性
    },
    'speed_pid': {
        'Kp': 5.0,    # 增加比例增益，补偿船舶纵向阻力增加
        'Ki': 0.2,    # 增加积分增益，确保能够达到目标速度
        'Kd': 2.0,    # 增加微分增益，提高稳定性
    }
}

# 碰撞避碰配置
COLLISION_AVOIDANCE_CONFIG = {
    'safe_distance': 500.0,  # 安全距离(m)
    'time_horizon': 600.0,   # 时间范围(s)
    'sampling_time': 10.0,   # 采样时间(s)

    # 碰撞风险评估参数
    'dcpa_threshold': 500.0,  # DCPA阈值(m)
    'tcpa_threshold': 600.0,  # TCPA阈值(s)
    'dist_threshold': 1000.0, # 相对距离阈值(m)
    'risk_weights': {
        'dcpa': 0.4,         # DCPA权重
        'tcpa': 0.3,         # TCPA权重
        'dist': 0.3          # 相对距离权重
    },

    # 碰撞场景配置
    'scenarios': {
        'crossing': {
            'own_ship': {
                'position': [0.0, 0.0],
                'heading': 0.0,
                'speed': 8.0
            },
            'target_ship': {
                'position': [2000.0, -2000.0],
                'heading': 45.0,
                'speed': 10.0
            },
            'duration': 1800.0  # 场景持续时间(s)
        },
        'head_on': {
            'own_ship': {
                'position': [0.0, 0.0],
                'heading': 0.0,
                'speed': 8.0
            },
            'target_ship': {
                'position': [3000.0, 100.0],
                'heading': 180.0,
                'speed': 8.0
            },
            'duration': 1200.0
        },
        'overtaking': {
            'own_ship': {
                'position': [0.0, 0.0],
                'heading': 0.0,
                'speed': 8.0
            },
            'target_ship': {
                'position': [500.0, 0.0],
                'heading': 0.0,
                'speed': 6.0
            },
            'duration': 1500.0
        }
    }
}

# 仿真参数
SIMULATION_CONFIG = {
    'dt': 0.1,               # 仿真步长(s)
    'total_time': 600.0,     # 减小总仿真时间到10分钟
    'visualization_step': 10  # 每10步更新一次可视化
}