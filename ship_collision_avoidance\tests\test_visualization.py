import matplotlib.pyplot as plt
import numpy as np
from matplotlib.gridspec import GridSpec
import seaborn as sns
from matplotlib import font_manager
import platform
from typing import List, Dict

# 配置中文字体
def setup_chinese_font():
    """配置中文字体支持"""
    system = platform.system()
    if system == "Windows":
        font_path = "C:/Windows/Fonts/msyh.ttc"  # 微软雅黑
    elif system == "Linux":
        font_path = "/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf"
    elif system == "Darwin":  # macOS
        font_path = "/System/Library/Fonts/PingFang.ttc"
    else:
        return None
    
    try:
        font_manager.fontManager.addfont(font_path)
        plt.rcParams['font.family'] = ['sans-serif']
        if system == "Windows":
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
        elif system == "Linux":
            plt.rcParams['font.sans-serif'] = ['Droid Sans Fallback']
        elif system == "Darwin":
            plt.rcParams['font.sans-serif'] = ['PingFang SC']
        plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
        return True
    except Exception as e:
        print(f"警告：中文字体配置失败 - {str(e)}")
        return False

class TestVisualizer:
    """测试结果可视化类"""
    
    def __init__(self):
        """初始化可视化器"""
        # 设置中文字体
        setup_chinese_font()
        
        # 使用seaborn的whitegrid样式
        sns.set_style("whitegrid")
        self.fig = plt.figure(figsize=(15, 10))
        self.gs = GridSpec(3, 3, figure=self.fig)
        
    def plot_initialization_test(self, model):
        """可视化初始化测试结果"""
        ax = self.fig.add_subplot(self.gs[0, 0])
        params = ['L', 'B', 'T', 'Cb', 'u', 'v', 'r', 'x', 'y', 'psi']
        values = [getattr(model, param) for param in params]
        ax.bar(params, values)
        ax.set_title('初始化测试')
        ax.tick_params(axis='x', rotation=45)
        
    def plot_force_calculation(self, X, Y, N):
        """可视化力计算测试结果"""
        ax = self.fig.add_subplot(self.gs[0, 1])
        forces = ['X', 'Y', 'N']
        values = [X, Y, N]
        ax.bar(forces, values)
        ax.set_title('力计算测试')
        
    def plot_state_update(self, states):
        """可视化状态更新测试结果"""
        ax = self.fig.add_subplot(self.gs[0, 2])
        ax.plot(states['x'], states['y'], 'b-', label='轨迹')
        ax.set_title('状态更新测试')
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.grid(True)
        ax.legend()
        
    def plot_turning_motion(self, states):
        """可视化转向运动测试结果"""
        ax = self.fig.add_subplot(self.gs[1, 0])
        ax.plot(states['x'], states['y'], 'r-', label='转向轨迹')
        ax.set_title('转向运动测试')
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.grid(True)
        ax.legend()
        
    def plot_straight_motion(self, states):
        """可视化直线运动测试结果"""
        ax = self.fig.add_subplot(self.gs[1, 1])
        time = np.arange(len(states['x']))
        ax.plot(time, states['y'], 'g-', label='横向位置')
        ax.set_title('直线运动测试')
        ax.set_xlabel('时间步')
        ax.set_ylabel('Y (m)')
        ax.grid(True)
        ax.legend()
        
    def plot_deceleration(self, speeds):
        """可视化减速性能测试结果"""
        ax = self.fig.add_subplot(self.gs[1, 2])
        time = np.arange(len(speeds))
        ax.plot(time, speeds, 'b-', label='速度')
        ax.set_title('减速性能测试')
        ax.set_xlabel('时间步')
        ax.set_ylabel('速度 (m/s)')
        ax.grid(True)
        ax.legend()
        
    def plot_invalid_inputs(self, max_rudder, max_rps):
        """可视化无效输入测试结果"""
        ax = self.fig.add_subplot(self.gs[2, 0])
        params = ['最大舵角', '最大转速']
        values = [max_rudder, max_rps]
        ax.bar(params, values, color='r')
        ax.set_title('无效输入测试')
        
    def plot_coefficients(self, coeffs):
        """可视化水动力系数测试结果"""
        ax = self.fig.add_subplot(self.gs[2, 1])
        coeff_names = ['Xvv', 'Yv', 'Nr']
        values = [coeffs.Xvv, coeffs.Yv, coeffs.Nr]
        ax.bar(coeff_names, values)
        ax.set_title('水动力系数测试')
        
    def plot_trajectory_comparison(self, actual_trajectory: List[Dict], 
                                 predicted_trajectory: List[Dict],
                                 title: str = "轨迹对比"):
        # 绘制实际轨迹和预测轨迹的对比
        ax = self.fig.add_subplot(self.gs[2, 2])
        ax.plot(actual_trajectory['x'], actual_trajectory['y'], 'b-', label='实际轨迹')
        ax.plot(predicted_trajectory['x'], predicted_trajectory['y'], 'r-', label='预测轨迹')
        ax.set_title(title)
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.grid(True)
        ax.legend()
        
    def plot_error_analysis(self, errors: List[float],
                           error_type: str = "位置",
                           title: str = "误差分析"):
        # 计算并显示统计数据
        # 绘制误差曲线
        
    def show(self):
        """显示所有测试结果"""
        plt.tight_layout()
        plt.show() 