# -*- coding: utf-8 -*-
"""
测试可视化器模块，用于可视化测试结果
"""
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Optional, Tuple
from .font_config import FontConfig

class TestVisualizer:
    def __init__(self):
        """初始化测试可视化器"""
        self.font_config = FontConfig()
        self.font_config.setup_chinese_font()
        
    def plot_trajectory_comparison(self, 
                                 actual_trajectory: List[Dict], 
                                 predicted_trajectory: List[Dict],
                                 title: str = "轨迹对比") -> None:
        """绘制实际轨迹和预测轨迹的对比图
        
        Args:
            actual_trajectory: 实际轨迹点列表
            predicted_trajectory: 预测轨迹点列表
            title: 图表标题
        """
        plt.figure(figsize=(10, 8))
        
        # 提取坐标
        actual_x = [point['x'] for point in actual_trajectory]
        actual_y = [point['y'] for point in actual_trajectory]
        pred_x = [point['x'] for point in predicted_trajectory]
        pred_y = [point['y'] for point in predicted_trajectory]
        
        # 绘制轨迹
        plt.plot(actual_x, actual_y, 'b-', label='实际轨迹')
        plt.plot(pred_x, pred_y, 'r--', label='预测轨迹')
        
        plt.title(title)
        plt.xlabel('X坐标 (m)')
        plt.ylabel('Y坐标 (m)')
        plt.legend()
        plt.grid(True)
        plt.axis('equal')
        
    def plot_error_analysis(self, 
                           errors: List[float],
                           error_type: str = "位置",
                           title: str = "误差分析") -> Tuple[float, float, float]:
        """绘制误差分析图
        
        Args:
            errors: 误差值列表
            error_type: 误差类型
            title: 图表标题
            
        Returns:
            Tuple[float, float, float]: (平均误差, 最大误差, 标准差)
        """
        plt.figure(figsize=(10, 6))
        
        # 计算统计数据
        mean_error = np.mean(errors)
        max_error = np.max(errors)
        std_error = np.std(errors)
        
        # 绘制误差曲线
        time_points = range(len(errors))
        plt.plot(time_points, errors, 'b-', label=f'{error_type}误差')
        plt.axhline(y=mean_error, color='r', linestyle='--', label='平均误差')
        
        plt.title(title)
        plt.xlabel('时间步')
        plt.ylabel(f'{error_type}误差')
        plt.legend()
        plt.grid(True)
        
        # 添加统计信息
        info_text = f'平均误差: {mean_error:.2f}\n'
        info_text += f'最大误差: {max_error:.2f}\n'
        info_text += f'标准差: {std_error:.2f}'
        plt.text(0.02, 0.98, info_text,
                transform=plt.gca().transAxes,
                verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
                
        return mean_error, max_error, std_error
        
    def plot_performance_metrics(self, 
                               metrics: Dict[str, float],
                               title: str = "性能指标") -> None:
        """绘制性能指标图
        
        Args:
            metrics: 性能指标字典
            title: 图表标题
        """
        plt.figure(figsize=(10, 6))
        
        # 准备数据
        names = list(metrics.keys())
        values = list(metrics.values())
        
        # 创建条形图
        bars = plt.bar(names, values)
        
        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.2f}',
                    ha='center', va='bottom')
        
        plt.title(title)
        plt.ylabel('数值')
        plt.grid(True, axis='y')
        
    def show(self) -> None:
        """显示所有图表"""
        plt.show()
        
    def save_figures(self, output_dir: str, prefix: str = "") -> None:
        """保存所有图表
        
        Args:
            output_dir: 输出目录
            prefix: 文件名前缀
        """
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        for i, fig in enumerate(plt.get_fignums()):
            plt.figure(fig)
            filename = f"{prefix}figure_{i+1}.png" if prefix else f"figure_{i+1}.png"
            filepath = os.path.join(output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            print(f"已保存图表: {filepath}")
            
    def clear(self) -> None:
        """清除所有图表"""
        plt.close('all') 