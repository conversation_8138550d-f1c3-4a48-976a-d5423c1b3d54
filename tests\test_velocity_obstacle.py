import pytest
import numpy as np
from ship_collision_avoidance.models.velocity_obstacle import VelocityObstacle, EncounterType

@pytest.fixture
def velocity_obstacle():
    """创建速度障碍对象的fixture"""
    return VelocityObstacle(safety_distance=500.0, time_horizon=300.0)

@pytest.fixture
def own_ship_state():
    """创建本船状态的fixture"""
    return {
        'x': 0.0,
        'y': 0.0,
        'psi': 0.0,  # 航向（度）
        'u': 5.0     # 速度（米/秒）
    }

@pytest.fixture
def target_ship_state():
    """创建目标船状态的fixture"""
    return {
        'x': 1000.0,
        'y': 0.0,
        'psi': 180.0,  # 航向（度）
        'u': 5.0       # 速度（米/秒）
    }

def test_calculate_velocity_obstacle(velocity_obstacle, own_ship_state, target_ship_state):
    """测试速度障碍区域计算"""
    # 计算速度障碍区域
    velocity_cone, apex_angle = velocity_obstacle.calculate_velocity_obstacle(
        own_ship_state, target_ship_state
    )
    
    # 验证返回值类型
    assert isinstance(velocity_cone, np.ndarray)
    assert isinstance(apex_angle, float)
    
    # 验证速度锥形区域的形状
    assert velocity_cone.shape == (3, 2)  # 三个顶点，每个顶点有x和y坐标
    
    # 验证顶角在合理范围内
    assert 0 < apex_angle < np.pi  # 顶角应该在0到π之间

def test_check_collision(velocity_obstacle, own_ship_state, target_ship_state):
    """测试碰撞检测功能"""
    # 初始状态下不应该有碰撞风险（距离1000米）
    assert not velocity_obstacle.check_collision(own_ship_state, target_ship_state)
    
    # 将目标船移动到安全距离以内
    close_target_state = target_ship_state.copy()
    close_target_state['x'] = 400.0  # 小于安全距离500米
    assert velocity_obstacle.check_collision(own_ship_state, close_target_state)
    
    # 测试相对静止状态
    static_target_state = target_ship_state.copy()
    static_target_state['u'] = 0.0
    own_ship_state_static = own_ship_state.copy()
    own_ship_state_static['u'] = 0.0
    collision_risk = velocity_obstacle.check_collision(
        own_ship_state_static, static_target_state
    )
    assert isinstance(collision_risk, bool)

def test_get_safe_velocity(velocity_obstacle, own_ship_state, target_ship_state):
    """测试安全速度计算"""
    # 计算安全速度
    safe_heading, safe_speed = velocity_obstacle.get_safe_velocity(
        own_ship_state, target_ship_state
    )
    
    # 验证返回值类型和范围
    assert isinstance(safe_heading, float)
    assert isinstance(safe_speed, float)
    assert 0 <= safe_heading < 360  # 航向应在0到360度之间
    assert safe_speed >= 0  # 速度应非负
    
    # 当没有碰撞风险时，应保持原始状态
    if not velocity_obstacle.check_collision(own_ship_state, target_ship_state):
        assert np.isclose(safe_heading, own_ship_state['psi'])
        assert np.isclose(safe_speed, own_ship_state['u'])

def test_emergency_maneuver(velocity_obstacle, own_ship_state, target_ship_state):
    """测试紧急避让功能"""
    # 设置最大变化限制
    max_heading_change = 30.0
    max_speed_change = 2.0
    
    # 计算紧急避让动作
    emergency_heading, emergency_speed = velocity_obstacle.emergency_maneuver(
        own_ship_state, target_ship_state,
        max_heading_change=max_heading_change,
        max_speed_change=max_speed_change
    )
    
    # 验证返回值类型和范围
    assert isinstance(emergency_heading, float)
    assert isinstance(emergency_speed, float)
    assert 0 <= emergency_heading < 360
    assert emergency_speed >= 0
    
    # 验证变化限制是否生效
    heading_change = abs(emergency_heading - own_ship_state['psi'])
    heading_change = min(heading_change, 360 - heading_change)  # 考虑跨越360度的情况
    assert heading_change <= max_heading_change
    
    speed_change = abs(emergency_speed - own_ship_state['u'])
    assert speed_change <= max_speed_change 

def test_determine_encounter_type():
    """测试会遇类型判断"""
    vo = VelocityObstacle()
    
    # 对遇场景
    own_ship = {'x': 0, 'y': 0, 'psi': 0, 'u': 5}
    target_ship = {'x': 1000, 'y': 0, 'psi': 180, 'u': 5}
    assert vo.determine_encounter_type(own_ship, target_ship) == EncounterType.HEAD_ON
    
    # 交叉相遇让路场景
    target_ship = {'x': 1000, 'y': 1000, 'psi': 270, 'u': 5}
    assert vo.determine_encounter_type(own_ship, target_ship) == EncounterType.CROSSING_GIVE_WAY
    
    # 交叉相遇直航场景
    target_ship = {'x': -1000, 'y': 1000, 'psi': 90, 'u': 5}
    assert vo.determine_encounter_type(own_ship, target_ship) == EncounterType.CROSSING_STAND_ON
    
    # 追越场景
    target_ship = {'x': 100, 'y': 0, 'psi': 0, 'u': 3}
    assert vo.determine_encounter_type(own_ship, target_ship) == EncounterType.OVERTAKING
    
    # 被追越场景
    target_ship = {'x': -100, 'y': 0, 'psi': 0, 'u': 7}
    assert vo.determine_encounter_type(own_ship, target_ship) == EncounterType.BEING_OVERTAKEN

def test_merge_velocity_obstacles():
    """测试速度障碍合并"""
    vo = VelocityObstacle()
    
    # 创建两个重叠的速度障碍
    apex1 = np.array([5, 0])
    legs1 = np.array([[1, 0], [0, 1]])
    apex2 = np.array([4, 1])
    legs2 = np.array([[0, 1], [-1, 0]])
    
    obstacles = [(apex1, legs1, 1.0), (apex2, legs2, 0.8)]
    merged = vo.merge_velocity_obstacles(obstacles)
    
    assert len(merged) == 1  # 应该合并为一个障碍
    merged_apex, merged_legs = merged[0]
    assert np.allclose(merged_apex, [4.5, 0.5])  # 检查合并后的顶点
    
def test_get_safe_velocity_multi_ship():
    """测试多船情况下的安全速度计算"""
    vo = VelocityObstacle()
    
    # 创建测试场景：本船和两艘目标船
    own_ship = {'x': 0, 'y': 0, 'psi': 0, 'u': 5}
    target_ships = [
        {'x': 1000, 'y': 0, 'psi': 180, 'u': 5},    # 对遇船
        {'x': 800, 'y': 800, 'psi': 270, 'u': 5}    # 交叉相遇船
    ]
    
    # 计算安全速度
    safe_heading, safe_speed = vo.get_safe_velocity_multi_ship(own_ship, target_ships)
    
    # 验证结果
    assert 0 <= safe_heading <= 360  # 航向应在有效范围内
    assert vo.min_speed <= safe_speed <= vo.max_speed  # 速度应在限制范围内
    
    # 验证空目标船列表的情况
    heading, speed = vo.get_safe_velocity_multi_ship(own_ship, [])
    assert heading == own_ship['psi']
    assert speed == own_ship['u']

def test_emergency_maneuver_multi_ship():
    """测试多船情况下的紧急避让"""
    vo = VelocityObstacle(safety_distance=500)
    
    # 创建危险场景：本船和多艘目标船
    own_ship = {'x': 0, 'y': 0, 'psi': 0, 'u': 5}
    target_ships = [
        {'x': 400, 'y': 0, 'psi': 180, 'u': 5},     # 危险对遇船
        {'x': 1000, 'y': 0, 'psi': 180, 'u': 5},    # 远距离对遇船
        {'x': 300, 'y': 300, 'psi': 270, 'u': 5}    # 危险交叉相遇船
    ]
    
    # 执行紧急避让
    emergency_heading, emergency_speed = vo.emergency_maneuver(
        own_ship, target_ships, max_heading_change=30, max_speed_change=2)
    
    # 验证结果
    assert 0 <= emergency_heading <= 360  # 航向应在有效范围内
    assert vo.min_speed <= emergency_speed <= vo.max_speed  # 速度应在限制范围内
    
    # 验证与当前状态的差异
    heading_change = abs(emergency_heading - own_ship['psi'])
    heading_change = min(heading_change, 360 - heading_change)
    assert heading_change <= 30  # 航向变化不应超过限制
    
    speed_change = abs(emergency_speed - own_ship['u'])
    assert speed_change <= 2  # 速度变化不应超过限制
    
    # 验证空目标船列表的情况
    heading, speed = vo.emergency_maneuver(own_ship, [], max_heading_change=30, max_speed_change=2)
    assert heading == own_ship['psi']
    assert speed == own_ship['u']

def test_calculate_maneuver_cost():
    """测试机动代价计算"""
    vo = VelocityObstacle()
    
    own_ship = {'x': 0, 'y': 0, 'psi': 0, 'u': 5}
    target_ships = [
        {'x': 1000, 'y': 0, 'psi': 180, 'u': 5},
        {'x': 800, 'y': 800, 'psi': 270, 'u': 5}
    ]
    
    # 计算不同机动的代价
    cost1 = vo._calculate_maneuver_cost(
        np.radians(0), 5.0,  # 当前状态
        np.radians(10), 5.5,  # 小幅度变化
        own_ship, target_ships
    )
    
    cost2 = vo._calculate_maneuver_cost(
        np.radians(0), 5.0,  # 当前状态
        np.radians(30), 7.0,  # 大幅度变化
        own_ship, target_ships
    )
    
    # 大幅度变化的代价应该更高
    assert cost1 < cost2 