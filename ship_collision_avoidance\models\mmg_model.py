# -*- coding: utf-8 -*-
"""
MMG船舶运动模型
"""
import numpy as np
from ship_collision_avoidance.config import SHIP_CONFIG
from .mmg_coefficients import MMGCoefficients

class MMGModel:
    def __init__(self):
        """初始化MMG模型参数"""
        self.L = SHIP_CONFIG['L']
        self.B = SHIP_CONFIG['B']
        self.T = SHIP_CONFIG['T']
        self.Cb = SHIP_CONFIG['Cb']
        self.mass = SHIP_CONFIG['mass']
        self.Izz = SHIP_CONFIG['Izz']
        self.xG = SHIP_CONFIG['xG']

        # 状态变量 - 强制初始化为0
        self.u = 0.0  # 纵向速度
        self.v = 0.0  # 横向速度 - 确保初始为0
        self.r = 0.0  # 角速度 - 确保初始为0
        self.x = 0.0  # 全局x坐标
        self.y = 0.0  # 全局y坐标
        self.psi = 0.0  # 艏向角

        # 限制参数
        self.max_rudder_angle = np.deg2rad(35.0)  # 最大舵角
        self.max_rps = 100.0  # 最大螺旋桨转速
        self.min_speed = 0.1  # 最小速度阈值

        # 添加调试计数器
        self.debug_counter = 0

        # 初始化水动力系数
        self._init_hydrodynamic_coefficients()

        # 打印初始状态
        print("MMG模型初始化完成:")
        print(f"  船长: {self.L:.1f} m")
        print(f"  船宽: {self.B:.1f} m")
        print(f"  吃水: {self.T:.1f} m")
        print(f"  方形系数: {self.Cb:.2f}")
        print(f"  质量: {self.mass:.1f} ton")
        print(f"  转动惯量: {self.Izz:.1e}")
        print(f"  初始角速度: {np.rad2deg(self.r):.4f}°/s")
        print(f"  初始横向速度: {self.v:.4f} m/s")

    def _init_hydrodynamic_coefficients(self):
        """初始化水动力系数"""
        # 船体力系数 - 根据实际船舶操纵性能彻底重新调整
        # 增加纵向阻力系数
        self.Xuu = -0.002 * self.mass  # 增加纵向阻力，使船舶减速更明显
        self.Xvv = -0.1 * self.mass    # 增加斜航阻力，使船舶在转向时减速更明显
        self.Xvr = 0.0005 * self.mass  # 减小耦合项
        self.Xrr = 0.002 * self.mass   # 减小耦合项

        # 增加航向稳定性相关系数
        # 对于航向稳定的船舶，Nv应为负值且绝对值较大，Nr应为负值且绝对值较大
        self.Yv = -0.8 * self.mass      # 大幅增加横向阻力，提高航向稳定性
        self.Yr = 0.01 * self.mass      # 减小回转引起的横向力
        self.Yv_dot = -0.4 * self.mass  # 增加横向附加质量
        self.Yr_dot = -0.1 * self.mass  # 增加回转附加质量

        # 调整回转力矩系数，确保航向稳定性和合理的旋回性能
        # 航向稳定性要求Nv为负且绝对值大，Nr为负且绝对值大
        self.Nv = -0.6 * self.mass * self.L    # 增大横向速度引起的回转力矩，提高航向稳定性
        self.Nr = -0.5 * self.mass * self.L    # 增大回转阻尼，提高航向稳定性
        self.Nv_dot = -0.05 * self.mass * self.L  # 调整横向附加转动惯量
        self.Nr_dot = -0.05 * self.mass * self.L * self.L  # 调整回转附加转动惯量

        # 螺旋桨系数 - 调整以更符合实际船舶特性
        self.D_prop = 0.7 * self.T
        self.w_P0 = 0.5 * self.Cb   # 增加伴流系数
        self.t_P = 0.2              # 增加推力减额系数
        self.kt_0 = 0.2             # 调整推力系数
        self.kt_1 = -0.2            # 调整推力系数
        self.kt_2 = -0.15           # 调整推力系数

        # 舵系数 - 调整以获得合理的舵效率
        self.A_R = 0.015 * self.L * self.T  # 舵面积
        self.lambda_R = 1.5                # 舵升力系数
        self.f_alpha = 1.5                 # 舵力系数
        self.epsilon = 0.6                 # 舵效率
        self.kappa = 0.3                   # 舵效率
        self.t_R = 0.25                    # 舵减推力系数
        self.x_R = -0.5 * self.L           # 舵位置
        self.a_H = 0.15                    # 舵增横向力系数
        self.x_H = -0.464 * self.L         # 舵增横向力作用点

    def _validate_inputs(self, delta_r, n_p):
        """验证输入参数的有效性

        Args:
            delta_r (float): 舵角(rad)
            n_p (float): 螺旋桨转速(rps)

        Raises:
            ValueError: 当输入超出有效范围时
        """
        if abs(delta_r) > self.max_rudder_angle:
            raise ValueError(f"舵角超出范围: {np.rad2deg(delta_r)}度")
        if n_p < 0 or n_p > self.max_rps:
            raise ValueError(f"螺旋桨转速无效: {n_p} RPS")

    def calculate_forces(self, delta_r, n_p):
        """计算船舶受力

        Args:
            delta_r (float): 舵角(rad)
            n_p (float): 螺旋桨转速(rps)

        Returns:
            tuple: (X, Y, N) 纵向力、横向力和首摇力矩
        """
        # 验证输入
        self._validate_inputs(delta_r, n_p)

        # 计算总速度和漂角
        U = np.sqrt(self.u**2 + self.v**2)
        if U < self.min_speed:
            U = self.min_speed
            beta = 0.0
        else:
            beta = np.arctan2(-self.v, self.u)

        # 计算船体力 - 使用更精确的公式
        # 纵向力
        X_H = (self.Xuu * self.u * abs(self.u) +  # 使用abs避免溢出
               self.Xvv * self.v * abs(self.v) +
               self.Xvr * self.v * self.r +
               self.Xrr * self.r * abs(self.r))

        # 横向力 - 增加航向稳定性相关项
        Y_H = (self.Yv * self.v +
               self.Yr * self.r +
               self.Yv_dot * U * beta +
               self.Yr_dot * U * self.r)

        # 首摇力矩 - 增加航向稳定性相关项
        # 对于航向稳定的船舶，Nv应为负值且绝对值较大，Nr应为负值且绝对值较大
        N_H = (self.Nv * self.v +
               self.Nr * self.r +
               self.Nv_dot * U * beta +
               self.Nr_dot * U * self.r)

        # 计算螺旋桨力 - 确保低速时也有合理的推力
        if n_p > 0:
            # 前进速度系数
            if self.u > self.min_speed:
                J = self.u * (1 - self.w_P0) / (n_p * self.D_prop)
            else:
                J = self.min_speed * (1 - self.w_P0) / (n_p * self.D_prop)

            J = np.clip(J, -1.0, 1.0)  # 限制J的范围

            # 推力系数
            KT = self.kt_0 + self.kt_1 * J + self.kt_2 * J * J

            # 确保KT不为负值
            KT = max(0.01, KT)

            # 计算推力
            T = KT * 1025 * n_p * n_p * self.D_prop**4

            # 考虑推力减额
            X_P = (1 - self.t_P) * T
        else:
            X_P = 0.0

        # 计算舵力 - 确保低速时舵效率降低
        # 舵入流速度
        u_R = self.u * (1 - self.w_P0)
        v_R = self.v + self.x_R * self.r

        # 计算有效入流角
        if abs(u_R) < self.min_speed:
            alpha_R = delta_r
            # 低速时降低舵效率
            efficiency_factor = abs(u_R) / self.min_speed
        else:
            alpha_R = delta_r - np.arctan2(v_R, u_R)
            efficiency_factor = 1.0

        # 计算舵法向力
        U_R2 = u_R**2 + v_R**2
        if U_R2 < self.min_speed**2:
            U_R2 = self.min_speed**2

        # 舵法向力
        F_N = 0.5 * 1025 * self.A_R * self.f_alpha * U_R2 * np.sin(alpha_R) * efficiency_factor

        # 计算舵力分量
        X_R = -(1 - self.t_R) * F_N * np.sin(delta_r)
        Y_R = -(1 + self.a_H) * F_N * np.cos(delta_r)
        N_R = -(self.x_R + self.a_H * self.x_H) * F_N * np.cos(delta_r)

        # 合成总力和力矩
        X = X_H + X_P + X_R
        Y = Y_H + Y_R
        N = N_H + N_R

        # 打印调试信息
        if hasattr(self, 'debug_counter'):
            self.debug_counter += 1
            if self.debug_counter % 100 == 0:  # 每100步打印一次
                print(f"力和力矩 - X: {X:.2f}, Y: {Y:.2f}, N: {N:.2f}")
                print(f"舵角: {np.rad2deg(delta_r):.2f}°, 螺旋桨转速: {n_p:.1f} RPS")
                print(f"舵力 - X_R: {X_R:.2f}, Y_R: {Y_R:.2f}, N_R: {N_R:.2f}")

        return X, Y, N

    def update_state(self, X, Y, N, dt):
        """更新船舶状态

        Args:
            X (float): 纵向力
            Y (float): 横向力
            N (float): 首摇力矩
            dt (float): 时间步长
        """
        # 强制限制角速度，确保不超过实际船舶的最大角速度
        # 实际船舶旋回一圈约需400秒，最大角速度约为0.9度/秒
        MAX_ANGULAR_VELOCITY = np.deg2rad(0.9)  # 最大角速度0.9度/秒
        self.r = np.clip(self.r, -MAX_ANGULAR_VELOCITY, MAX_ANGULAR_VELOCITY)

        # 强制限制横向速度，确保不超过合理范围
        # 实际船舶横向速度通常不超过纵向速度的15%
        max_lateral_speed = 0.15 * self.u
        self.v = np.clip(self.v, -max_lateral_speed, max_lateral_speed)

        # 计算加速度 - 考虑附加质量和附加转动惯量
        m11 = self.mass - self.Xvv  # 纵向附加质量
        m22 = self.mass - self.Yv_dot  # 横向附加质量
        m66 = self.Izz - self.Nr_dot  # 附加转动惯量

        # 考虑附加质量的运动方程
        u_dot = (X + m22 * self.v * self.r) / m11
        v_dot = (Y - m11 * self.u * self.r) / m22
        r_dot = N / m66

        # 严格限制加速度，特别是角加速度
        u_dot = np.clip(u_dot, -0.5, 0.5)     # 大幅减小纵向加速度
        v_dot = np.clip(v_dot, -0.2, 0.2)     # 大幅减小横向加速度

        # 角加速度限制非常重要，必须确保船舶不会快速旋转
        # 实际船舶角加速度通常很小，使其达到最大角速度需要较长时间
        MAX_ANGULAR_ACCELERATION = np.deg2rad(0.05)  # 最大角加速度0.05度/秒²
        r_dot = np.clip(r_dot, -MAX_ANGULAR_ACCELERATION, MAX_ANGULAR_ACCELERATION)

        # 更新速度
        self.u += u_dot * dt
        self.v += v_dot * dt
        self.r += r_dot * dt

        # 再次限制速度，确保更新后的速度仍在合理范围内
        max_speed = 7.2  # 14节，约7.2 m/s

        # 限制纵向速度
        self.u = np.clip(self.u, 0.0, max_speed)

        # 再次限制横向速度
        max_lateral_speed = 0.15 * self.u
        self.v = np.clip(self.v, -max_lateral_speed, max_lateral_speed)

        # 再次限制角速度
        self.r = np.clip(self.r, -MAX_ANGULAR_VELOCITY, MAX_ANGULAR_VELOCITY)

        # 更新位置和艏向
        self.x += (self.u * np.cos(self.psi) - self.v * np.sin(self.psi)) * dt
        self.y += (self.u * np.sin(self.psi) + self.v * np.cos(self.psi)) * dt
        self.psi = (self.psi + self.r * dt) % (2 * np.pi)  # 保持艏向角在[0, 2π]范围内

        # 模拟实际船舶在旋回时的速度变化特性
        # 旋回时，由于横向阻力增加，纵向速度会自然减小
        if abs(self.r) > np.deg2rad(0.1) and self.u > 0.5 * max_speed:
            # 当船舶转向且速度较高时，模拟速度降低
            speed_reduction = 0.1 * abs(np.rad2deg(self.r)) * dt  # 根据角速度大小计算速度降低量
            self.u = max(0.5 * max_speed, self.u - speed_reduction)  # 确保速度不低于最大速度的50%

        # 打印当前角速度（度/秒）和横向速度，用于调试
        if hasattr(self, 'debug_counter'):
            self.debug_counter += 1
            if self.debug_counter % 100 == 0:  # 每100步打印一次
                print(f"DEBUG - 角速度: {np.rad2deg(self.r):.4f}°/s, 横向速度: {self.v:.4f} m/s")
        else:
            self.debug_counter = 0

    def get_state(self):
        """获取当前状态

        Returns:
            dict: 包含所有状态变量的字典
        """
        return {
            'x': self.x,
            'y': self.y,
            'psi': self.psi,
            'u': self.u,
            'v': self.v,
            'r': self.r
        }