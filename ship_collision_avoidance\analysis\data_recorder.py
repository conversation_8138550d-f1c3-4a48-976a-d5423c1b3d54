# -*- coding: utf-8 -*-
"""
数据记录模块
"""
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any

class SimulationDataRecorder:
    def __init__(self, output_dir: str = 'simulation_results'):
        """初始化数据记录器
        
        Args:
            output_dir (str): 输出目录
        """
        self.output_dir = output_dir
        self.simulation_id = datetime.now().strftime('%Y%m%d_%H%M%S')
        self.data = {
            'metadata': {},
            'time_series': [],
            'events': [],
            'performance_metrics': {}
        }
        
        # 创建输出目录
        os.makedirs(os.path.join(output_dir, self.simulation_id), exist_ok=True)
        
    def record_metadata(self, scenario_config: Dict, ship_config: Dict):
        """记录仿真元数据
        
        Args:
            scenario_config (Dict): 场景配置
            ship_config (Dict): 船舶配置
        """
        self.data['metadata'] = {
            'simulation_id': self.simulation_id,
            'timestamp': datetime.now().isoformat(),
            'scenario_config': scenario_config,
            'ship_config': ship_config
        }
        
    def record_state(self, time: float, own_ship: Dict, 
                    target_ships: List[Dict], control_inputs: Dict,
                    risk_assessment: Dict):
        """记录时间序列数据
        
        Args:
            time (float): 仿真时间
            own_ship (Dict): 本船状态
            target_ships (List[Dict]): 目标船状态
            control_inputs (Dict): 控制输入
            risk_assessment (Dict): 风险评估结果
        """
        state_data = {
            'time': time,
            'own_ship': own_ship.copy(),
            'target_ships': [ship.copy() for ship in target_ships],
            'control_inputs': control_inputs.copy(),
            'risk_assessment': risk_assessment.copy()
        }
        self.data['time_series'].append(state_data)
        
    def record_event(self, time: float, event_type: str, 
                    description: str, data: Dict = None):
        """记录关键事件
        
        Args:
            time (float): 事件发生时间
            event_type (str): 事件类型
            description (str): 事件描述
            data (Dict, optional): 相关数据
        """
        event = {
            'time': time,
            'type': event_type,
            'description': description,
            'data': data if data is not None else {}
        }
        self.data['events'].append(event)
        
    def calculate_performance_metrics(self):
        """计算性能指标"""
        time_series = pd.DataFrame(self.data['time_series'])
        
        # 提取轨迹数据
        own_ship_positions = np.array([(state['own_ship']['x'], 
                                      state['own_ship']['y']) 
                                     for state in self.data['time_series']])
        
        # 计算性能指标
        metrics = {
            'simulation_duration': time_series['time'].max(),
            'min_safety_distance': self._calculate_min_safety_distance(),
            'path_deviation': self._calculate_path_deviation(own_ship_positions),
            'control_effort': self._calculate_control_effort(),
            'risk_statistics': self._calculate_risk_statistics(),
            'collision_avoidance_success': self._evaluate_collision_avoidance()
        }
        
        self.data['performance_metrics'] = metrics
        
    def _calculate_min_safety_distance(self) -> float:
        """计算最小安全距离"""
        min_distance = float('inf')
        
        for state in self.data['time_series']:
            own_pos = np.array([state['own_ship']['x'], 
                              state['own_ship']['y']])
            
            for target in state['target_ships']:
                target_pos = np.array([target['x'], target['y']])
                distance = np.linalg.norm(own_pos - target_pos)
                min_distance = min(min_distance, distance)
                
        return min_distance
    
    def _calculate_path_deviation(self, positions: np.ndarray) -> float:
        """计算航路偏离度
        
        Args:
            positions (np.ndarray): 位置序列
            
        Returns:
            float: 航路偏离度
        """
        if len(positions) < 2:
            return 0.0
            
        # 计算起点和终点之间的直线距离
        start_pos = positions[0]
        end_pos = positions[-1]
        direct_distance = np.linalg.norm(end_pos - start_pos)
        
        # 计算实际航行距离
        actual_distance = 0.0
        for i in range(len(positions)-1):
            actual_distance += np.linalg.norm(positions[i+1] - positions[i])
            
        # 计算航路偏离度
        return (actual_distance - direct_distance) / direct_distance
    
    def _calculate_control_effort(self) -> Dict:
        """计算控制器努力度
        
        Returns:
            Dict: 控制器性能指标
        """
        control_inputs = [state['control_inputs'] 
                         for state in self.data['time_series']]
        
        # 计算舵角变化
        rudder_angles = [input.get('rudder_angle', 0) 
                        for input in control_inputs]
        rudder_changes = np.diff(rudder_angles)
        
        # 计算速度变化
        speeds = [input.get('propeller_rps', 0) 
                 for input in control_inputs]
        speed_changes = np.diff(speeds)
        
        return {
            'total_rudder_change': np.sum(np.abs(rudder_changes)),
            'mean_rudder_change': np.mean(np.abs(rudder_changes)),
            'total_speed_change': np.sum(np.abs(speed_changes)),
            'mean_speed_change': np.mean(np.abs(speed_changes))
        }
    
    def _calculate_risk_statistics(self) -> Dict:
        """计算风险统计指标
        
        Returns:
            Dict: 风险统计指标
        """
        risk_values = [state['risk_assessment']['highest_risk'] 
                      for state in self.data['time_series']]
        
        return {
            'max_risk': np.max(risk_values),
            'mean_risk': np.mean(risk_values),
            'risk_std': np.std(risk_values),
            'time_in_high_risk': np.sum(np.array(risk_values) > 0.7) / len(risk_values)
        }
    
    def _evaluate_collision_avoidance(self) -> Dict:
        """评估避碰成功情况
        
        Returns:
            Dict: 避碰评估结果
        """
        min_distance = self._calculate_min_safety_distance()
        risk_stats = self._calculate_risk_statistics()
        
        return {
            'success': min_distance > 500.0,  # 假设安全距离为500米
            'min_distance': min_distance,
            'max_risk_encountered': risk_stats['max_risk'],
            'risk_exposure_time': risk_stats['time_in_high_risk']
        }
    
    def save_results(self):
        """保存仿真结果"""
        # 计算性能指标
        self.calculate_performance_metrics()
        
        # 保存原始数据
        output_path = os.path.join(self.output_dir, self.simulation_id)
        
        # 保存元数据和性能指标
        with open(os.path.join(output_path, 'metadata.json'), 'w') as f:
            json.dump({
                'metadata': self.data['metadata'],
                'performance_metrics': self.data['performance_metrics']
            }, f, indent=2)
        
        # 保存时间序列数据
        df = pd.DataFrame(self.data['time_series'])
        df.to_csv(os.path.join(output_path, 'time_series.csv'), index=False)
        
        # 保存事件记录
        with open(os.path.join(output_path, 'events.json'), 'w') as f:
            json.dump(self.data['events'], f, indent=2)
            
        print(f"Results saved to {output_path}")