# -*- coding: utf-8 -*-
"""
轨迹与速度障碍可视化模块
"""
import numpy as np
import matplotlib.pyplot as plt
import platform
from matplotlib.patches import Circle, Polygon, Arrow, Ellipse
from typing import Dict, List, Tuple, Optional
from .font_config import FontConfig

class CollisionAvoidanceVisualizer:
    def __init__(self):
        """初始化可视化器"""
        # 设置中文字体
        self.font_config = FontConfig()

        # 在Windows系统上优先使用微软雅黑字体
        if platform.system() == 'Windows':
            # 设置matplotlib全局字体为微软雅黑
            plt.rcParams['font.family'] = 'sans-serif'
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
            plt.rcParams['axes.unicode_minus'] = False
            self.font_config.set_font('Microsoft YaHei')
            print("已设置微软雅黑字体")
        else:
            # 非Windows系统使用默认中文字体
            font_path = self.font_config.setup_chinese_font()
            if not font_path:
                print("警告：使用系统默认字体")

        self.fig = None
        self.ax = None
        self.trajectory_lines = {}
        self.ship_markers = {}
        self.info_text = None

        # 初始化字体配置
        self.update_font_settings()

    def update_font_settings(self, font_size: int = 12, title_size: int = 14):
        """
        更新字体设置

        Args:
            font_size (int): 基本字体大小
            title_size (int): 标题字体大小
        """
        font_info = self.font_config.get_font_info()
        if font_info:
            plt.rcParams.update({
                'font.size': font_size,
                'axes.titlesize': title_size,
                'axes.labelsize': font_size,
                'xtick.labelsize': font_size,
                'ytick.labelsize': font_size,
                'legend.fontsize': font_size,
                'font.family': font_info.get('family', 'sans-serif'),
                'font.sans-serif': [font_info.get('name', 'Arial')]
            })

    def set_title(self, title: str, fontsize: int = None):
        """
        设置图表标题，支持中文

        Args:
            title (str): 标题文本
            fontsize (int, optional): 字体大小
        """
        if self.ax:
            font_info = self.font_config.get_font_info()
            self.ax.set_title(title, fontsize=fontsize,
                            fontfamily=font_info.get('family', 'sans-serif') if font_info else None)

    def set_axis_labels(self, xlabel: str = None, ylabel: str = None, fontsize: int = None):
        """
        设置坐标轴标签，支持中文

        Args:
            xlabel (str, optional): x轴标签
            ylabel (str, optional): y轴标签
            fontsize (int, optional): 字体大小
        """
        if self.ax:
            font_info = self.font_config.get_font_info()
            font_family = font_info.get('family', 'sans-serif') if font_info else None
            if xlabel:
                self.ax.set_xlabel(xlabel, fontsize=fontsize, fontfamily=font_family)
            if ylabel:
                self.ax.set_ylabel(ylabel, fontsize=fontsize, fontfamily=font_family)

    def set_legend(self, labels: List[str], fontsize: int = None, loc: str = 'best'):
        """
        设置图例，支持中文

        Args:
            labels (List[str]): 图例标签列表
            fontsize (int, optional): 字体大小
            loc (str, optional): 图例位置
        """
        if self.ax:
            font_info = self.font_config.get_font_info()
            font_props = {'family': font_info.get('family', 'sans-serif')} if font_info else {}
            if fontsize:
                font_props['size'] = fontsize
            self.ax.legend(labels, loc=loc, prop=font_props)

    def add_text(self, x: float, y: float, text: str, fontsize: int = None, **kwargs):
        """
        在图表中添加文本，支持中文

        Args:
            x (float): x坐标
            y (float): y坐标
            text (str): 文本内容
            fontsize (int, optional): 字体大小
            **kwargs: 其他文本属性
        """
        if self.ax:
            font_info = self.font_config.get_font_info()
            if font_info:
                kwargs['family'] = font_info.get('family', 'sans-serif')
            if fontsize:
                kwargs['fontsize'] = fontsize
            self.ax.text(x, y, text, **kwargs)

    def setup_plot(self, xlim: Tuple[float, float], ylim: Tuple[float, float]):
        """设置绘图区域

        Args:
            xlim (Tuple[float, float]): x轴范围
            ylim (Tuple[float, float]): y轴范围
        """
        self.fig, self.ax = plt.subplots(figsize=(12, 8))
        self.ax.set_xlim(xlim)
        self.ax.set_ylim(ylim)
        self.ax.grid(True)
        self.ax.set_aspect('equal')
        self.ax.set_xlabel('X (m)')
        self.ax.set_ylabel('Y (m)')
        self.ax.set_title('船舶避碰仿真可视化')

    def draw_ship(self, ship_state: Dict, ship_id: str, color: str = 'blue',
                 is_target: bool = False):
        """绘制船舶

        Args:
            ship_state (Dict): 船舶状态
            ship_id (str): 船舶ID
            color (str): 船舶颜色
            is_target (bool): 是否为目标船
        """
        # 船舶尺寸
        length = ship_state.get('length', 50.0)
        width = ship_state.get('width', 10.0)

        # 计算船舶轮廓点
        heading = ship_state['psi']
        bow_x = ship_state['x'] + length/2 * np.cos(heading)
        bow_y = ship_state['y'] + length/2 * np.sin(heading)
        stern_x = ship_state['x'] - length/2 * np.cos(heading)
        stern_y = ship_state['y'] - length/2 * np.sin(heading)
        port_x = stern_x + width/2 * np.cos(heading + np.pi/2)
        port_y = stern_y + width/2 * np.sin(heading + np.pi/2)
        starboard_x = stern_x + width/2 * np.cos(heading - np.pi/2)
        starboard_y = stern_y + width/2 * np.sin(heading - np.pi/2)

        # 绘制船舶
        ship_polygon = Polygon(np.array([[bow_x, bow_y],
                                       [port_x, port_y],
                                       [starboard_x, starboard_y]]),
                             color=color, alpha=0.5)

        # 更新或添加船舶标记
        if ship_id in self.ship_markers:
            try:
                self.ship_markers[ship_id].remove()
            except Exception:
                # 如果移除失败，忽略错误
                pass
        self.ship_markers[ship_id] = ship_polygon
        self.ax.add_patch(ship_polygon)

        # 添加船舶标签
        label = '目标船' if is_target else '本船'
        self.ax.text(ship_state['x'], ship_state['y'],
                    f'{label}\n({ship_state["x"]:.0f}, {ship_state["y"]:.0f})',
                    horizontalalignment='center')

    def draw_trajectory(self, trajectory: List[Tuple[float, float]],
                       ship_id: str, color: str = 'blue'):
        """绘制轨迹

        Args:
            trajectory (List[Tuple[float, float]]): 轨迹点列表
            ship_id (str): 船舶ID
            color (str): 轨迹颜色
        """
        x_points, y_points = zip(*trajectory)

        # 更新轨迹线
        if ship_id in self.trajectory_lines:
            try:
                self.trajectory_lines[ship_id].remove()
            except Exception:
                # 如果移除失败，忽略错误
                pass
        line, = self.ax.plot(x_points, y_points, color=color,
                           linestyle='--', alpha=0.5)
        self.trajectory_lines[ship_id] = line

    def draw_velocity_obstacle(self, vo_apex: np.ndarray, left_bound: np.ndarray,
                             right_bound: np.ndarray, own_vel: np.ndarray,
                             safe_vel: Optional[np.ndarray] = None,
                             max_speed: float = 7.2):  # 最大速度设为7.2 m/s
        """绘制速度障碍区域

        Args:
            vo_apex: 速度障碍顶点（目标船速度）
            left_bound: 左边界向量
            right_bound: 右边界向量
            own_vel: 本船当前速度向量
            safe_vel: 计算得到的安全速度向量
            max_speed: 最大速度限制
        """
        try:
            # 创建一个小的子图用于显示速度空间
            # 在主图的右下角创建一个速度空间子图
            if not hasattr(self, 'vo_ax') or self.vo_ax is None:
                self.vo_ax = self.fig.add_axes([0.65, 0.1, 0.25, 0.25], facecolor='#f0f0f0')
                self.vo_axes = [self.vo_ax]  # 保存子图引用列表

            # 清除之前的内容
            self.vo_ax.clear()

            # 设置子图属性
            self.vo_ax.set_aspect('equal')
            self.vo_ax.set_title('速度障碍分析 (VO)', fontsize=10)
            self.vo_ax.grid(True, linestyle='--', alpha=0.7)

            # 设置速度空间的范围
            self.vo_ax.set_xlim([-max_speed, max_speed])
            self.vo_ax.set_ylim([-max_speed, max_speed])
            self.vo_ax.set_xlabel('Vx (m/s)', fontsize=8)
            self.vo_ax.set_ylabel('Vy (m/s)', fontsize=8)

            # 绘制速度约束圆（最大速度限制）
            speed_circle = plt.Circle((0, 0), max_speed, color='blue',
                                     fill=False, linestyle='--', alpha=0.5)
            self.vo_ax.add_patch(speed_circle)

            # 计算VO区域的顶点
            # 扩展左右边界向量，使其足够长以覆盖整个速度空间
            scale_factor = max_speed * 3
            left_extended = vo_apex + left_bound * scale_factor
            right_extended = vo_apex + right_bound * scale_factor

            # 创建VO区域多边形
            vo_vertices = np.array([vo_apex, left_extended, right_extended])
            vo_polygon = Polygon(vo_vertices, color='red', alpha=0.3)
            self.vo_ax.add_patch(vo_polygon)

            # 绘制VO边界线，使其更明显
            self.vo_ax.plot([vo_apex[0], left_extended[0]], [vo_apex[1], left_extended[1]],
                      'r-', linewidth=1.5, alpha=0.7)
            self.vo_ax.plot([vo_apex[0], right_extended[0]], [vo_apex[1], right_extended[1]],
                      'r-', linewidth=1.5, alpha=0.7)

            # 绘制当前速度向量
            self.vo_ax.arrow(0, 0, own_vel[0], own_vel[1],
                       head_width=0.3, head_length=0.5, fc='blue', ec='blue',
                       length_includes_head=True, alpha=0.7)
            self.vo_ax.plot(own_vel[0], own_vel[1], 'bo', markersize=4)
            self.vo_ax.text(own_vel[0], own_vel[1], '  当前速度', fontsize=8, color='blue')

            # 如果有安全速度，绘制安全速度向量
            if safe_vel is not None:
                self.vo_ax.arrow(0, 0, safe_vel[0], safe_vel[1],
                           head_width=0.3, head_length=0.5, fc='green', ec='green',
                           length_includes_head=True, alpha=0.7)
                self.vo_ax.plot(safe_vel[0], safe_vel[1], 'go', markersize=4)
                self.vo_ax.text(safe_vel[0], safe_vel[1], '  安全速度', fontsize=8, color='green')

            # 绘制目标船速度（VO顶点）
            self.vo_ax.plot(vo_apex[0], vo_apex[1], 'ro', markersize=4)
            self.vo_ax.text(vo_apex[0], vo_apex[1], '  目标船速度', fontsize=8, color='red')

            # 计算并显示可航行区间
            # 使用更密集的网格来显示可行驶速度域
            grid_size = 20  # 网格大小
            x = np.linspace(-max_speed, max_speed, grid_size)
            y = np.linspace(-max_speed, max_speed, grid_size)
            X, Y = np.meshgrid(x, y)

            # 创建掩码数组，标记哪些点在速度约束内且不在VO内
            mask = np.zeros((grid_size, grid_size), dtype=bool)

            for i in range(grid_size):
                for j in range(grid_size):
                    point = np.array([X[i, j], Y[i, j]])

                    # 检查点是否在速度约束圆内
                    in_speed_limit = np.linalg.norm(point) <= max_speed

                    # 检查点是否在VO外
                    if in_speed_limit:
                        # 计算点相对于VO顶点的向量
                        rel_vel = point - vo_apex

                        # 检查是否在边界内
                        if np.linalg.norm(rel_vel) > 1e-6:  # 避免除以零
                            rel_vel_norm = rel_vel / np.linalg.norm(rel_vel)
                            cross_left = np.cross(left_bound, rel_vel_norm)
                            cross_right = np.cross(right_bound, rel_vel_norm)

                            # 如果点不在VO内，标记为可行驶区域
                            if not (cross_left >= 0 and cross_right <= 0):
                                mask[i, j] = True

            # 绘制可行驶速度域
            for i in range(grid_size):
                for j in range(grid_size):
                    if mask[i, j]:
                        self.vo_ax.plot(X[i, j], Y[i, j], 'go', markersize=2, alpha=0.3)

            # 在圆周上均匀取点，显示可行驶速度域的边界
            angles = np.linspace(0, 2*np.pi, 100)
            circle_points_x = max_speed * np.cos(angles)
            circle_points_y = max_speed * np.sin(angles)

            # 判断每个点是否在VO内
            for i in range(len(angles)):
                point = np.array([circle_points_x[i], circle_points_y[i]])
                # 计算点相对于VO顶点的向量
                rel_vel = point - vo_apex

                # 检查是否在边界内
                if np.linalg.norm(rel_vel) > 1e-6:  # 避免除以零
                    rel_vel_norm = rel_vel / np.linalg.norm(rel_vel)
                    cross_left = np.cross(left_bound, rel_vel_norm)
                    cross_right = np.cross(right_bound, rel_vel_norm)

                    # 如果点不在VO内，标记为可行驶区域边界
                    if not (cross_left >= 0 and cross_right <= 0):
                        self.vo_ax.plot(circle_points_x[i], circle_points_y[i], 'go', markersize=3, alpha=0.7)

            # 添加图例说明
            self.vo_ax.text(-max_speed*0.95, max_speed*0.85,
                      '红色区域: 速度障碍\n绿色区域: 可行驶速度域\n蓝色: 当前速度\n绿色箭头: 安全速度',
                      fontsize=8, bbox=dict(facecolor='white', alpha=0.7))

            # 在主图上也添加VO信息文本
            vo_info = [
                "速度障碍分析:",
                f"目标船速度: ({vo_apex[0]:.1f}, {vo_apex[1]:.1f}) m/s",
                f"本船速度: ({own_vel[0]:.1f}, {own_vel[1]:.1f}) m/s"
            ]

            if safe_vel is not None:
                vo_info.append(f"安全速度: ({safe_vel[0]:.1f}, {safe_vel[1]:.1f}) m/s")

            # 添加VO信息文本
            text_x = self.ax.get_xlim()[1] - 400
            text_y = self.ax.get_ylim()[0] + 200

            # 创建文本对象
            vo_text = self.ax.text(
                text_x, text_y,
                '\n'.join(vo_info),
                fontsize=10,
                bbox=dict(facecolor='white', alpha=0.7),
                verticalalignment='bottom'
            )

            # 保存文本对象引用，以便后续清除
            if not hasattr(self, 'vo_text'):
                self.vo_text = None

            # 如果之前有文本，先移除
            if self.vo_text is not None:
                try:
                    self.vo_text.remove()
                except Exception:
                    pass

            self.vo_text = vo_text

        except Exception as e:
            print(f"绘制速度障碍区域时出错: {str(e)}")

    def draw_safety_domain(self, position: Tuple[float, float],
                          radius: float, color: str = 'green',
                          is_own_ship: bool = False, heading: float = 0.0):
        """绘制安全域

        Args:
            position (Tuple[float, float]): 中心位置
            radius (float): 安全域半径
            color (str): 颜色
            is_own_ship (bool): 是否为本船
            heading (float): 船舶航向（弧度）
        """
        if is_own_ship:
            # 为本船绘制椭圆形安全域，考虑航向
            # 椭圆长轴为2*radius，短轴为1.5*radius
            a = radius * 2.0  # 长轴
            b = radius * 1.5  # 短轴

            # 创建椭圆
            ellipse = Ellipse(position, width=a, height=b,
                            angle=np.rad2deg(heading),
                            color=color, alpha=0.2, fill=True,
                            label='本船安全域')
            self.ax.add_patch(ellipse)

            # 添加安全域边界线，使其更明显
            ellipse_border = Ellipse(position, width=a, height=b,
                                   angle=np.rad2deg(heading),
                                   edgecolor=color, facecolor='none',
                                   linestyle='--', linewidth=2)
            self.ax.add_patch(ellipse_border)

            # 添加标签
            self.ax.text(position[0], position[1] + b/2 + 50,
                        f"本船安全域 (半径: {radius:.0f}m)",
                        color=color, fontsize=10,
                        horizontalalignment='center')
        else:
            # 为目标船绘制圆形安全域
            safety_circle = Circle(position, radius,
                                 color=color, alpha=0.1, fill=True)
            self.ax.add_patch(safety_circle)

    def draw_risk_indicator(self, position: Tuple[float, float],
                          risk_index: float):
        """绘制风险指示器

        Args:
            position (Tuple[float, float]): 显示位置
            risk_index (float): 风险指标 [0,1]
        """
        # 根据风险等级选择颜色
        if risk_index < 0.3:
            color = 'green'
            risk_level = "安全"
        elif risk_index < 0.4:
            color = 'green'
            risk_level = "低风险"
        elif risk_index < 0.6:
            color = 'yellow'
            risk_level = "一般风险"
        elif risk_index < 0.8:
            color = 'orange'
            risk_level = "紧迫局面"
        else:
            color = 'red'
            risk_level = "紧迫危险"

        # 创建风险指示器背景
        circle = Circle(position, 60, color=color, alpha=0.3)
        self.ax.add_patch(circle)

        # 显示风险指数和等级
        self.ax.text(position[0], position[1] - 15,
                    f'风险指数: {risk_index:.2f}',
                    color=color,
                    fontsize=10,
                    horizontalalignment='center',
                    verticalalignment='center',
                    bbox=dict(facecolor='white', alpha=0.7))

        self.ax.text(position[0], position[1] + 15,
                    f'风险等级: {risk_level}',
                    color=color,
                    fontsize=10,
                    fontweight='bold',
                    horizontalalignment='center',
                    verticalalignment='center',
                    bbox=dict(facecolor='white', alpha=0.7))

    def draw_predicted_path(self, current_state: Dict,
                          predicted_states: List[Dict],
                          color: str = 'cyan'):
        """绘制预测路径

        Args:
            current_state (Dict): 当前状态
            predicted_states (List[Dict]): 预测状态列表
            color (str): 路径颜色
        """
        try:
            # 验证输入
            if not predicted_states:
                return

            x_points = [state['x'] for state in predicted_states]
            y_points = [state['y'] for state in predicted_states]

            self.ax.plot(x_points, y_points,
                        color=color, linestyle=':', alpha=0.5,
                        label='预测路径')

        except Exception as e:
            print(f"绘制预测路径时出错: {str(e)}")

    def add_legend(self):
        """添加图例"""
        self.ax.legend()

    def update(self):
        """更新显示"""
        plt.draw()
        plt.pause(0.01)

    def clear(self):
        """清除所有图形元素"""
        if self.ax:
            self.ax.cla()
            self.trajectory_lines = {}
            self.ship_markers = {}

            # 清除VO文本
            if hasattr(self, 'vo_text') and self.vo_text is not None:
                try:
                    self.vo_text.remove()
                except Exception:
                    # 如果移除失败，忽略错误
                    pass
                self.vo_text = None

            # 清除VO子图
            if hasattr(self, 'vo_ax') and self.vo_ax is not None:
                try:
                    self.vo_ax.clear()
                except Exception:
                    # 如果清除失败，忽略错误
                    pass

            # 确保VO子图列表存在
            if not hasattr(self, 'vo_axes'):
                self.vo_axes = []

    def save_figure(self, filename: str):
        """保存图形

        Args:
            filename (str): 文件名
        """
        self.fig.savefig(filename, dpi=300, bbox_inches='tight')

    def close(self):
        """关闭图形"""
        if self.fig:
            plt.close(self.fig)

        # 关闭所有matplotlib窗口
        plt.close('all')

        # 清除所有引用
        self.fig = None
        self.ax = None
        if hasattr(self, 'vo_ax'):
            self.vo_ax = None
        if hasattr(self, 'vo_text'):
            self.vo_text = None
        self.trajectory_lines = {}
        self.ship_markers = {}

    def plot_trajectory_analysis(self, sim_data: Dict = None):
        """绘制轨迹分析图

        Args:
            sim_data (Dict, optional): 仿真数据字典
        """
        try:
            plt.figure(figsize=(12, 8))

            if sim_data is None:
                # 如果没有提供数据，显示提示信息
                plt.text(0.5, 0.5, "没有可用的轨迹数据",
                        horizontalalignment='center',
                        verticalalignment='center',
                        transform=plt.gca().transAxes)
            else:
                # 绘制本船轨迹
                if 'own_ship' in sim_data:
                    own_ship_data = sim_data['own_ship']
                    plt.plot(own_ship_data['x'], own_ship_data['y'], 'b-', label='本船轨迹')

                # 绘制目标船轨迹
                if 'target_ships' in sim_data:
                    for i, target_data in enumerate(sim_data['target_ships']):
                        plt.plot(target_data['x'], target_data['y'], 'r-',
                                label=f'目标船{i+1}轨迹')

                plt.legend()
                plt.grid(True)
                plt.xlabel('X (m)')
                plt.ylabel('Y (m)')
                plt.title('船舶轨迹分析')

        except Exception as e:
            print(f"绘制轨迹分析图时出错: {str(e)}")

    def draw_info_text(self, own_ship_state: Dict, target_ships_states: List[Dict],
                      control_outputs: Optional[Dict] = None):
        """绘制船舶信息文本

        Args:
            own_ship_state (dict): 本船状态
            target_ships_states (list): 目标船舶状态列表
            control_outputs (dict, optional): 控制输出
        """
        try:
            # 清除之前的文本
            if hasattr(self, '_info_texts'):
                for text in self._info_texts:
                    try:
                        text.remove()
                    except Exception:
                        # 如果移除失败，忽略错误
                        pass
            self._info_texts = []

            # 验证输入
            if not isinstance(own_ship_state, dict):
                raise ValueError("own_ship_state必须是字典类型")
            if not isinstance(target_ships_states, list):
                raise ValueError("target_ships_states必须是列表类型")

            # 本船信息
            own_ship_info = [
                f"本船状态:",
                f"位置: ({own_ship_state['x']:.1f}, {own_ship_state['y']:.1f})",
                f"航向: {np.rad2deg(own_ship_state['psi']):.1f}°",
                f"速度: {np.sqrt(own_ship_state['u']**2 + own_ship_state['v']**2):.1f} m/s"
            ]

            # 添加控制输出信息
            if control_outputs:
                # 基本控制信息
                own_ship_info.extend([
                    f"舵角: {control_outputs.get('delta_r', 0):.1f}°",
                    f"转速: {control_outputs.get('n_p', 0):.1f} rpm"
                ])

                # 添加避让模式信息
                avoidance_mode = control_outputs.get('avoidance_mode', 'none')
                encounter_type = control_outputs.get('encounter_type', 'unknown')

                # 避让模式中文显示
                mode_display = {
                    'none': '无避让',
                    'course_change': '改向避让',
                    'speed_change': '变速避让',
                    'combined': '改向+变速',
                    'emergency': '紧急避让'
                }.get(avoidance_mode, '未知')

                # 会遇类型中文显示
                encounter_display = {
                    'unknown': '未知',
                    'head_on': '对遇',
                    'crossing': '交叉相遇',
                    'overtaking': '追越'
                }.get(encounter_type, '未知')

                # 添加避让信息
                own_ship_info.extend([
                    f"避让模式: {mode_display}",
                    f"会遇类型: {encounter_display}"
                ])

            # 在左侧显示本船信息
            text = self.ax.text(
                self.ax.get_xlim()[0] + 100,
                self.ax.get_ylim()[1] - 100,
                '\n'.join(own_ship_info),
                fontsize=10,
                verticalalignment='top'
            )
            self._info_texts.append(text)

            # 目标船信息
            target_info = ["目标船信息:"]
            for i, target in enumerate(target_ships_states):
                target_info.extend([
                    f"\n目标船 {i+1}:",
                    f"位置: ({target['x']:.1f}, {target['y']:.1f})",
                    f"航向: {np.rad2deg(target['psi']):.1f}°",
                    f"速度: {target['u']:.1f} m/s",
                    f"风险指数: {target.get('risk_index', 0):.3f}"
                ])

            # 在右侧显示目标船信息
            text = self.ax.text(
                self.ax.get_xlim()[1] - 400,
                self.ax.get_ylim()[1] - 100,
                '\n'.join(target_info),
                fontsize=10,
                verticalalignment='top'
            )
            self._info_texts.append(text)

        except Exception as e:
            print(f"显示信息时出错: {str(e)}")

    def draw_velocity_vector(self, start_pos: Tuple[float, float],
                           velocity: Tuple[float, float], color: str = 'blue',
                           scale: float = 1.0, label: str = None):
        """绘制速度向量

        Args:
            start_pos (Tuple[float, float]): 向量起点坐标
            velocity (Tuple[float, float]): 速度向量 (vx, vy)
            color (str): 向量颜色
            scale (float): 缩放因子，用于调整向量长度
            label (str): 向量标签
        """
        try:
            # 计算向量终点
            end_x = start_pos[0] + velocity[0] * scale
            end_y = start_pos[1] + velocity[1] * scale

            # 计算向量长度和方向
            dx = velocity[0] * scale
            dy = velocity[1] * scale

            # 创建箭头
            arrow = Arrow(start_pos[0], start_pos[1], dx, dy,
                        width=2.0, color=color, alpha=0.7)

            # 添加到图中
            self.ax.add_patch(arrow)

            # 如果有标签，添加文本标注
            if label:
                self.add_text(end_x, end_y, label,
                            horizontalalignment='left',
                            verticalalignment='bottom',
                            color=color)

        except Exception as e:
            print(f"绘制速度向量时出错: {str(e)}")