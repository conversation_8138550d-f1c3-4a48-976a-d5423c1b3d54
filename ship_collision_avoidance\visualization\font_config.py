# -*- coding: utf-8 -*-
"""
字体配置模块，用于处理中文字体显示
"""
import os
import sys
import platform
from matplotlib import font_manager
import matplotlib.pyplot as plt
from typing import List, Optional, Dict
import matplotlib.font_manager as fm

class FontConfig:
    """字体配置管理类"""

    def __init__(self):
        """初始化字体配置"""
        self.system = platform.system()
        self.font_info = None
        # 定义各操作系统下的中文字体路径
        self.font_paths: Dict[str, List[str]] = {
            'Windows': [
                'C:/Windows/Fonts/msyh.ttc',      # 微软雅黑
                'C:/Windows/Fonts/simsun.ttc',    # 宋体
                'C:/Windows/Fonts/simhei.ttf',    # 黑体
                'C:/Windows/Fonts/simkai.ttf',    # 楷体
            ],
            'Linux': [
                '/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf',
                '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc',
                '/usr/share/fonts/truetype/arphic/uming.ttc',
                '/usr/share/fonts/truetype/arphic/ukai.ttc',
            ],
            'Darwin': [  # macOS
                '/System/Library/Fonts/PingFang.ttc',
                '/System/Library/Fonts/STHeiti Light.ttc',
                '/System/Library/Fonts/STHeiti Medium.ttc',
                '/Library/Fonts/Songti.ttc',
            ]
        }

        # 定义字体家族名称
        self.font_families: Dict[str, List[str]] = {
            'Windows': ['Microsoft YaHei', 'SimSun', 'SimHei', 'KaiTi'],
            'Linux': ['Droid Sans Fallback', 'Noto Sans CJK SC', 'AR PL UMing CN', 'AR PL UKai CN'],
            'Darwin': ['PingFang SC', 'STHeiti', 'Songti SC']
        }

        # 初始化时自动设置微软雅黑字体（Windows系统）
        if self.system == 'Windows':
            self.set_font('Microsoft YaHei')
            # 设置matplotlib全局字体
            plt.rcParams['font.family'] = 'sans-serif'
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
            plt.rcParams['axes.unicode_minus'] = False

    def _get_default_font(self) -> Dict[str, str]:
        """
        获取默认字体配置

        Returns:
            Dict[str, str]: 字体配置信息
        """
        if self.system == 'Windows':
            return {
                'family': 'Microsoft YaHei',
                'weight': 'normal',
                'size': '10'
            }
        elif self.system == 'Linux':
            return {
                'family': 'WenQuanYi Micro Hei',
                'weight': 'normal',
                'size': '10'
            }
        else:  # macOS
            return {
                'family': 'PingFang SC',
                'weight': 'normal',
                'size': '10'
            }

    def set_font(self, family: str, weight: str = 'normal', size: str = '10'):
        """
        设置字体

        Args:
            family (str): 字体族
            weight (str, optional): 字重
            size (str, optional): 字体大小
        """
        self.font_info = {
            'family': family,
            'weight': weight,
            'size': size
        }

    def get_font_info(self) -> Optional[Dict]:
        """获取字体信息

        Returns:
            Dict: 字体信息字典，包含path、family和name
        """
        return self.font_info

    def verify_font(self, family: str) -> bool:
        """
        验证字体是否可用

        Args:
            family (str): 字体族名称

        Returns:
            bool: 字体是否可用
        """
        return family.lower() in [f.name.lower() for f in fm.fontManager.ttflist]

    def list_available_fonts(self) -> list:
        """
        列出系统中所有可用的字体

        Returns:
            list: 可用字体列表
        """
        return sorted([f.name for f in fm.fontManager.ttflist])

    def get_font_path(self, family: str) -> Optional[str]:
        """
        获取指定字体的文件路径

        Args:
            family (str): 字体族名称

        Returns:
            Optional[str]: 字体文件路径，如果不存在则返回 None
        """
        for font in fm.fontManager.ttflist:
            if font.name.lower() == family.lower():
                return font.fname
        return None

    def detect_available_fonts(self) -> List[str]:
        """检测系统中可用的中文字体

        Returns:
            List[str]: 可用字体路径列表
        """
        available_fonts = []
        if self.system in self.font_paths:
            for font_path in self.font_paths[self.system]:
                if os.path.exists(font_path):
                    available_fonts.append(font_path)
        return available_fonts

    def setup_chinese_font(self) -> Optional[str]:
        """设置中文字体

        Returns:
            str: 字体路径，如果找不到合适的字体则返回None
        """
        try:
            if self.system == 'Windows':
                font_paths = [
                    'C:/Windows/Fonts/msyh.ttc',    # 微软雅黑（优先使用）
                    'C:/Windows/Fonts/simhei.ttf',  # 黑体
                    'C:/Windows/Fonts/simsun.ttc'   # 宋体
                ]
            elif self.system == 'Linux':
                font_paths = [
                    '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
                    '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc'
                ]
            elif self.system == 'Darwin':  # macOS
                font_paths = [
                    '/System/Library/Fonts/PingFang.ttc',
                    '/System/Library/Fonts/STHeiti Light.ttc'
                ]
            else:
                print(f"警告：未知操作系统 {self.system}")
                return None

            # 查找第一个存在的字体文件
            for font_path in font_paths:
                if os.path.exists(font_path):
                    self.font_info = {
                        'path': font_path,
                        'family': 'sans-serif',
                        'name': os.path.splitext(os.path.basename(font_path))[0]
                    }
                    return font_path

            print("警告：未找到合适的中文字体")
            return None

        except Exception as e:
            print(f"设置中文字体时出错: {str(e)}")
            return None

    def apply_font_config(self):
        """
        应用字体配置到 matplotlib
        """
        font_info = self.get_font_info()
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['font.sans-serif'] = [font_info['family']]
        plt.rcParams['axes.unicode_minus'] = False

    def get_system_default_font(self) -> str:
        """
        获取系统默认字体

        Returns:
            str: 系统默认字体名称
        """
        if self.system == 'Windows':
            return 'Microsoft YaHei'
        elif self.system == 'Linux':
            return 'WenQuanYi Micro Hei'
        else:  # macOS
            return 'PingFang SC'

    def get_current_matplotlib_config(self) -> Dict:
        """
        获取当前 matplotlib 字体配置

        Returns:
            Dict: matplotlib 字体配置信息
        """
        return {
            'family': plt.rcParams['font.family'][0] if isinstance(plt.rcParams['font.family'], list) else plt.rcParams['font.family'],
            'sans-serif': plt.rcParams['font.sans-serif'],
            'unicode_minus': plt.rcParams['axes.unicode_minus']
        }

    def reset_to_default(self):
        """
        重置为默认字体配置
        """
        self.font_info = self._get_default_font()
        self.apply_font_config()

    def is_cjk_font(self, family: str) -> bool:
        """
        检查字体是否支持 CJK 字符

        Args:
            family (str): 字体族名称

        Returns:
            bool: 是否支持 CJK 字符
        """
        try:
            # 获取字体路径
            font_path = self.get_font_path(family)
            if not font_path:
                print(f"警告：未找到字体 {family} 的路径")
                return False

            # 检查字体文件是否存在
            if not os.path.exists(font_path):
                print(f"警告：字体文件不存在：{font_path}")
                return False

            # 使用 fontTools 检查字体
            from fontTools.ttLib import TTFont
            font = TTFont(font_path)

            # 检查字体的 cmap 表
            for table in font['cmap'].tables:
                if table.isUnicode():
                    # 检查一些常见的中文字符
                    test_chars = ['中', '文', '字', '体']
                    for char in test_chars:
                        if ord(char) in table.cmap:
                            return True

            return False

        except Exception as e:
            print(f"警告：检查字体 {family} 时发生错误：{str(e)}")
            # 对于 Windows 系统，如果是常见的中文字体，直接返回 True
            if self.system == 'Windows' and family in ['Microsoft YaHei', 'SimSun', 'SimHei', 'KaiTi']:
                return True
            return False

    def plot_risk_analysis(self, sim_id: str = None):
        """
        绘制风险分析图

        Args:
            sim_id (str, optional): 仿真ID，用于区分不同的仿真结果
        """
        try:
            plt.figure(figsize=(10, 6))

            # 如果没有指定sim_id，则显示所有仿真结果
            if sim_id is None:
                # 在这里添加具体的绘图逻辑
                pass
            else:
                # 显示指定仿真的结果
                pass

            plt.title('风险分析')
            plt.xlabel('时间')
            plt.ylabel('风险指标')
            plt.grid(True)
            plt.legend()

        except Exception as e:
            print(f"绘制风险分析图时出错: {str(e)}")