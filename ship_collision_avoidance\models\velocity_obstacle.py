import numpy as np
from typing import Dict, Tuple, List, Optional
from enum import Enum

class EncounterType(Enum):
    """会遇类型枚举"""
    HEAD_ON = "head_on"         # 对遇
    CROSSING_GIVE_WAY = "crossing_give_way"   # 交叉相遇让路
    CROSSING_STAND_ON = "crossing_stand_on"   # 交叉相遇直航
    OVERTAKING = "overtaking"   # 追越
    BEING_OVERTAKEN = "being_overtaken"  # 被追越

class VelocityObstacle:
    """速度障碍类，用于计算碰撞风险和安全速度"""
    
    def __init__(self, safety_distance: float = 500.0, time_horizon: float = 300.0):
        """
        初始化速度障碍对象
        
        Args:
            safety_distance: 安全距离（米）
            time_horizon: 时间范围（秒）
        """
        self.safety_distance = safety_distance
        self.time_horizon = time_horizon
        
        # 船舶操纵性能约束
        self.max_speed = 12.0        # 最大速度(m/s)
        self.min_speed = 2.0         # 最小速度(m/s)
        self.max_acceleration = 0.5   # 最大加速度(m/s^2)
        self.max_deceleration = -0.5  # 最大减速度(m/s^2)
        self.max_heading_rate = np.deg2rad(20.0)  # 最大转向速率(rad/s)
        
        # 避让优先级权重
        self.priority_weights = {
            EncounterType.HEAD_ON: 1.0,
            EncounterType.CROSSING_GIVE_WAY: 1.2,
            EncounterType.CROSSING_STAND_ON: 0.8,
            EncounterType.OVERTAKING: 0.6,
            EncounterType.BEING_OVERTAKEN: 0.4
        }
        
    def calculate_velocity_obstacle(self, own_ship_state: Dict, target_ship_state: Dict) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        计算速度障碍区域
        
        Args:
            own_ship_state: 本船状态字典，包含位置、航向和速度
            target_ship_state: 目标船状态字典，包含位置、航向和速度
            
        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: 
                - vo_apex: 速度障碍顶点（目标船速度）
                - left_bound: 左边界向量
                - right_bound: 右边界向量
        """
        # 提取状态信息
        own_pos = np.array([own_ship_state['x'], own_ship_state['y']])
        target_pos = np.array([target_ship_state['x'], target_ship_state['y']])
        
        # 计算目标船速度向量（速度障碍顶点）
        target_vel = target_ship_state['u'] * np.array([
            np.cos(np.radians(target_ship_state['psi'])),
            np.sin(np.radians(target_ship_state['psi']))
        ])
        
        # 计算相对位置和距离
        relative_pos = target_pos - own_pos
        distance = np.linalg.norm(relative_pos)
        
        # 计算碰撞锥角度（基于安全距离）
        alpha = np.arcsin(self.safety_distance / distance)
        
        # 计算相对位置的方向角
        theta = np.arctan2(relative_pos[1], relative_pos[0])
        
        # 计算速度障碍的左右边界角度
        left_angle = theta + alpha
        right_angle = theta - alpha
        
        # 计算边界向量（单位向量）
        left_bound = np.array([np.cos(left_angle), np.sin(left_angle)])
        right_bound = np.array([np.cos(right_angle), np.sin(right_angle)])
        
        # 调整边界向量的大小（基于最大速度）
        left_bound *= self.max_speed
        right_bound *= self.max_speed
        
        return target_vel, left_bound, right_bound
    
    def check_collision(self, own_ship_state: Dict, target_ship_state: Dict) -> bool:
        """
        检查是否存在碰撞风险
        
        Args:
            own_ship_state: 本船状态
            target_ship_state: 目标船状态
            
        Returns:
            bool: 是否存在碰撞风险
        """
        # 计算相对位置和速度
        own_pos = np.array([own_ship_state['x'], own_ship_state['y']])
        target_pos = np.array([target_ship_state['x'], target_ship_state['y']])
        
        own_vel = own_ship_state['u'] * np.array([
            np.cos(np.radians(own_ship_state['psi'])),
            np.sin(np.radians(own_ship_state['psi']))
        ])
        
        target_vel = target_ship_state['u'] * np.array([
            np.cos(np.radians(target_ship_state['psi'])),
            np.sin(np.radians(target_ship_state['psi']))
        ])
        
        relative_pos = target_pos - own_pos
        relative_vel = target_vel - own_vel
        
        # 计算当前距离
        current_distance = np.linalg.norm(relative_pos)
        if current_distance < self.safety_distance:
            return True
            
        # 如果相对速度几乎为零，使用当前距离判断
        relative_speed = np.linalg.norm(relative_vel)
        if relative_speed < 1e-6:
            return False
            
        # 计算相对运动方向与相对位置的夹角
        relative_direction = np.arctan2(relative_vel[1], relative_vel[0])
        position_direction = np.arctan2(relative_pos[1], relative_pos[0])
        angle_diff = abs(relative_direction - position_direction)
        if angle_diff > np.pi:
            angle_diff = 2 * np.pi - angle_diff
            
        # 如果相对运动方向与相对位置方向的夹角大于90度，说明两船在远离
        if angle_diff > np.pi/2:
            return False
            
        # 计算最近点距离（CPA）
        t_cpa = -np.dot(relative_pos, relative_vel) / (relative_speed * relative_speed)
        
        # 如果CPA时间为负或超过时间范围，使用当前距离判断
        if t_cpa < 0 or t_cpa > self.time_horizon:
            return False
            
        # 计算CPA位置和距离
        cpa_pos = relative_pos + t_cpa * relative_vel
        cpa_distance = np.linalg.norm(cpa_pos)
        
        return cpa_distance < self.safety_distance
    
    def get_safe_velocity(self, own_ship_state: Dict, target_ship_state: Dict) -> Tuple[float, float]:
        """
        计算安全速度
        
        Args:
            own_ship_state: 本船状态
            target_ship_state: 目标船状态
            
        Returns:
            Tuple[float, float]: 安全航向（度）和速度（米/秒）
        """
        if not self.check_collision(own_ship_state, target_ship_state):
            return own_ship_state['psi'], own_ship_state['u']
            
        # 计算速度障碍
        vo_apex, left_bound, right_bound = self.calculate_velocity_obstacle(
            own_ship_state, target_ship_state)
            
        # 计算当前速度向量
        current_vel = own_ship_state['u'] * np.array([
            np.cos(np.radians(own_ship_state['psi'])),
            np.sin(np.radians(own_ship_state['psi']))
        ])
        
        # 在速度空间中搜索安全速度
        best_vel = current_vel
        min_cost = float('inf')
        
        # 生成候选速度（在速度-航向空间中）
        speeds = np.linspace(self.min_speed, self.max_speed, 15)
        headings = np.linspace(0, 2*np.pi, 36)  # 每10度一个采样点
        
        for speed in speeds:
            for heading in headings:
                # 计算候选速度向量
                vel = speed * np.array([np.cos(heading), np.sin(heading)])
                
                # 检查是否在速度障碍区域内
                if not self._is_in_velocity_obstacle(vel, vo_apex, left_bound, right_bound):
                    # 计算代价
                    cost = self._calculate_velocity_cost(
                        current_vel, vel, own_ship_state, target_ship_state)
                    
                    if cost < min_cost:
                        min_cost = cost
                        best_vel = vel
        
        # 如果没有找到安全速度，使用紧急避让
        if min_cost == float('inf'):
            return self.emergency_maneuver(own_ship_state, [target_ship_state])
        
        # 转换回航向和速度
        safe_speed = np.linalg.norm(best_vel)
        safe_heading = np.degrees(np.arctan2(best_vel[1], best_vel[0])) % 360
        
        return safe_heading, safe_speed
        
    def _calculate_velocity_cost(self, current_vel: np.ndarray, new_vel: np.ndarray,
                               own_ship_state: Dict, target_ship_state: Dict) -> float:
        """计算速度变化的代价"""
        # 速度变化代价
        vel_change_cost = np.linalg.norm(new_vel - current_vel) / self.max_speed
        
        # 航向变化代价
        current_heading = np.radians(own_ship_state['psi'])
        new_heading = np.arctan2(new_vel[1], new_vel[0])
        heading_change = abs(new_heading - current_heading)
        if heading_change > np.pi:
            heading_change = 2 * np.pi - heading_change
        heading_cost = heading_change / np.pi
        
        # 与目标船的距离代价
        dx = target_ship_state['x'] - own_ship_state['x']
        dy = target_ship_state['y'] - own_ship_state['y']
        distance = np.sqrt(dx*dx + dy*dy)
        distance_cost = max(0, 1 - distance / (2 * self.safety_distance))
        
        # 综合代价（权重可调）
        return 0.4 * vel_change_cost + 0.4 * heading_cost + 0.2 * distance_cost
        
    def _is_in_velocity_obstacle(self, vel: np.ndarray, vo_apex: np.ndarray,
                               left_bound: np.ndarray, right_bound: np.ndarray) -> bool:
        """
        判断速度是否在速度障碍区域内
        
        Args:
            vel: 待检查的速度向量
            vo_apex: 速度障碍顶点（目标船速度）
            left_bound: 左边界向量
            right_bound: 右边界向量
            
        Returns:
            bool: 是否在速度障碍区域内
        """
        # 计算相对速度
        rel_vel = vel - vo_apex
        
        # 计算与边界向量的叉积
        cross_left = np.cross(left_bound, rel_vel)
        cross_right = np.cross(rel_vel, right_bound)
        
        # 如果两个叉积都为正，说明在速度障碍区域内
        return cross_left >= 0 and cross_right >= 0
    
    def determine_encounter_type(self, own_ship_state: Dict, target_ship_state: Dict) -> EncounterType:
        """
        判断会遇类型
        
        Args:
            own_ship_state: 本船状态
            target_ship_state: 目标船状态
            
        Returns:
            EncounterType: 会遇类型
        """
        # 计算相对方位
        dx = target_ship_state['x'] - own_ship_state['x']
        dy = target_ship_state['y'] - own_ship_state['y']
        relative_bearing = np.arctan2(dy, dx)
        own_heading = np.radians(own_ship_state['psi'])
        target_heading = np.radians(target_ship_state['psi'])
        
        # 计算相对方位角
        bearing_from_own = (relative_bearing - own_heading) % (2 * np.pi)
        bearing_from_target = (relative_bearing - target_heading + np.pi) % (2 * np.pi)
        
        # 根据COLREGS规则判断会遇类型
        if abs(bearing_from_own - np.pi) < np.pi/4:
            return EncounterType.HEAD_ON  # 对遇
        elif bearing_from_own < np.pi * 5/6:
            return EncounterType.CROSSING_GIVE_WAY  # 交叉让路
        elif bearing_from_target < np.pi * 5/6 and bearing_from_own > np.pi/6:
            return EncounterType.CROSSING_STAND_ON
        elif bearing_from_own < np.pi/3:
            return EncounterType.OVERTAKING
        else:
            return EncounterType.BEING_OVERTAKEN
            
    def merge_velocity_obstacles(self, velocity_obstacles: List[Tuple[np.ndarray, np.ndarray, float]]) -> List[Tuple[np.ndarray, np.ndarray]]:
        """
        合并多个速度障碍
        
        Args:
            velocity_obstacles: 速度障碍列表，每个元素为(apex, legs, priority)
            
        Returns:
            List[Tuple[np.ndarray, np.ndarray]]: 合并后的速度障碍列表
        """
        if not velocity_obstacles:
            return []
            
        # 按优先级排序
        sorted_obstacles = sorted(velocity_obstacles, key=lambda x: x[2], reverse=True)
        merged = []
        
        for apex, legs, _ in sorted_obstacles:
            # 检查是否与已有障碍重叠
            overlap = False
            for existing_apex, existing_legs in merged:
                if self._check_obstacle_overlap(apex, legs, existing_apex, existing_legs):
                    # 合并重叠的障碍
                    merged_apex, merged_legs = self._merge_two_obstacles(
                        apex, legs, existing_apex, existing_legs)
                    merged.remove((existing_apex, existing_legs))
                    merged.append((merged_apex, merged_legs))
                    overlap = True
                    break
            if not overlap:
                merged.append((apex, legs))
                
        return merged
        
    def _check_obstacle_overlap(self, apex1: np.ndarray, legs1: np.ndarray,
                              apex2: np.ndarray, legs2: np.ndarray) -> bool:
        """检查两个速度障碍是否重叠"""
        # 简化判断：检查顶点距离和边界方向
        return np.linalg.norm(apex1 - apex2) < self.safety_distance
        
    def _merge_two_obstacles(self, apex1: np.ndarray, legs1: np.ndarray,
                           apex2: np.ndarray, legs2: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """合并两个重叠的速度障碍"""
        # 选择更保守的合并策略
        merged_apex = (apex1 + apex2) / 2
        angles = []
        for legs in [legs1, legs2]:
            for leg in legs:
                angles.append(np.arctan2(leg[1], leg[0]))
        min_angle = min(angles)
        max_angle = max(angles)
        
        # 创建新的边界
        merged_legs = np.array([
            [np.cos(min_angle), np.sin(min_angle)],
            [np.cos(max_angle), np.sin(max_angle)]
        ])
        
        return merged_apex, merged_legs
        
    def get_safe_velocity_multi_ship(self, own_ship_state: Dict, target_ships_states: List[Dict]) -> Tuple[float, float]:
        """
        计算多船情况下的安全速度
        
        Args:
            own_ship_state: 本船状态
            target_ships_states: 目标船状态列表
            
        Returns:
            Tuple[float, float]: 安全航向（度）和速度（米/秒）
        """
        if not target_ships_states:
            return own_ship_state['psi'], own_ship_state['u']
            
        # 计算每个目标船的速度障碍
        velocity_obstacles = []
        for target_state in target_ships_states:
            encounter_type = self.determine_encounter_type(own_ship_state, target_state)
            priority = self.priority_weights[encounter_type]
            apex, legs = self.calculate_velocity_obstacle(own_ship_state, target_state)
            velocity_obstacles.append((apex, legs, priority))
            
        # 合并速度障碍
        merged_obstacles = self.merge_velocity_obstacles(velocity_obstacles)
        
        # 如果没有有效的速度障碍，保持当前状态
        if not merged_obstacles:
            return own_ship_state['psi'], own_ship_state['u']
            
        # 生成候选速度
        current_heading = np.radians(own_ship_state['psi'])
        current_speed = own_ship_state['u']
        
        best_heading = current_heading
        best_speed = current_speed
        min_cost = float('inf')
        
        # 在可行范围内搜索最优速度
        for dh in np.linspace(-self.max_heading_rate, self.max_heading_rate, 21):
            for ds in np.linspace(
                max(-self.max_deceleration, self.min_speed - current_speed),
                min(self.max_acceleration, self.max_speed - current_speed),
                11
            ):
                new_heading = current_heading + dh
                new_speed = current_speed + ds
                
                # 检查是否安全
                if self._is_velocity_safe(new_heading, new_speed, merged_obstacles):
                    # 计算代价（考虑航向和速度变化）
                    cost = self._calculate_maneuver_cost(
                        current_heading, current_speed,
                        new_heading, new_speed,
                        own_ship_state, target_ships_states
                    )
                    
                    if cost < min_cost:
                        min_cost = cost
                        best_heading = new_heading
                        best_speed = new_speed
                        
        return np.degrees(best_heading) % 360, best_speed
        
    def _calculate_maneuver_cost(self, current_heading: float, current_speed: float,
                               new_heading: float, new_speed: float,
                               own_ship_state: Dict, target_ships_states: List[Dict]) -> float:
        """计算机动代价"""
        # 航向变化代价（40%权重）
        heading_change = abs(new_heading - current_heading)
        heading_cost = heading_change / self.max_heading_rate
        
        # 速度变化代价（30%权重）
        speed_change = abs(new_speed - current_speed)
        speed_cost = speed_change / self.max_acceleration
        
        # 距离代价（30%权重）
        distance_cost = sum(max(0, 1 - distance / self.safety_distance)
                           for target_state in target_ships_states)
        
        return 0.4 * heading_cost + 0.3 * speed_cost + 0.3 * distance_cost
        
    def emergency_maneuver(self, own_ship_state: Dict, target_ships_states: List[Dict],
                         max_heading_change: float = 30.0,
                         max_speed_change: float = 2.0) -> Tuple[float, float]:
        """
        计算多船情况下的紧急避让机动
        
        Args:
            own_ship_state: 本船状态
            target_ships_states: 目标船状态列表
            max_heading_change: 最大航向变化（度）
            max_speed_change: 最大速度变化（米/秒）
            
        Returns:
            Tuple[float, float]: 紧急避让的航向和速度
        """
        # 找到最危险目标
        most_dangerous_target = min(target_ships_states,
                                  key=lambda x: distance_to_own_ship(x))
        
        # 选择避让方向（垂直于相对方位）
        dx = most_dangerous_target['x'] - own_ship_state['x']
        dy = most_dangerous_target['y'] - own_ship_state['y']
        target_bearing = np.arctan2(dy, dx)
        
        relative_heading = target_bearing - np.radians(own_ship_state['psi'])
        if np.sin(relative_heading) > 0:
            emergency_heading = own_ship_state['psi'] + max_heading_change
        else:
            emergency_heading = own_ship_state['psi'] - max_heading_change
            
        # 根据距离调整速度
        min_distance = np.sqrt(dx*dx + dy*dy)
        if min_distance < self.safety_distance:
            emergency_speed = max(self.min_speed,
                                own_ship_state['u'] - max_speed_change)
        else:
            # 获取推荐的安全速度
            safe_heading, safe_speed = self.get_safe_velocity_multi_ship(
                own_ship_state, target_ships_states)
            emergency_speed = safe_speed
            
        return emergency_heading % 360, emergency_speed
    
    def draw_velocity_obstacle(self, vo_apex, left_boundary, right_boundary,
                             position, radius=1000):
        # 绘制边界线
        x_left = [position[0], position[0] + radius * np.cos(left_boundary)]
        y_left = [position[1], position[1] + radius * np.sin(left_boundary)]
        
        # 填充障碍区域
        angles = np.linspace(left_boundary, right_boundary, 100)
        x_fill = position[0] + radius * np.cos(angles)
        y_fill = position[1] + radius * np.sin(angles) 