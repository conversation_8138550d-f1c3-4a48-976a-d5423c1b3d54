# -*- coding: utf-8 -*-
"""
模糊自适应PID控制器
"""
import numpy as np
import skfuzzy as fuzz
from skfuzzy import control as ctrl
from ship_collision_avoidance.config import CONTROL_CONFIG
import logging

class FuzzyPIDController:
    def __init__(self, control_type='heading', kp=0.5, ki=0.3, kd=0.2, alpha=0.3):
        """初始化模糊PID控制器

        Args:
            control_type (str): 控制类型，'heading'或'speed'
            kp (float): 比例增益基础值
            ki (float): 积分增益基础值
            kd (float): 微分增益基础值
            alpha (float): 输出平滑系数
        """
        self.control_type = control_type
        self.pid_params = CONTROL_CONFIG[f'{control_type}_pid']

        # 基础PID参数
        self.kp_base = kp
        self.ki_base = ki
        self.kd_base = kd

        # 当前PID参数
        self.kp = kp
        self.ki = ki
        self.kd = kd

        # 增益调整系数
        self.kp_gain = 0.5  # 增大增益系数
        self.ki_gain = 0.3
        self.kd_gain = 0.3

        # 其他参数
        self.alpha = alpha
        self.integral = 0
        self.prev_error = 0
        self.prev_output = 0
        self.integral_limit = 50  # 减小积分限幅

        # 模糊控制器设置
        self.error_range = [-1, 1]  # 归一化后的误差范围
        self.error_dot_range = [-1, 1]  # 归一化后的误差变化率范围
        self.output_range = [-1, 1]  # 归一化后的输出范围

        # 日志设置
        self.logger = logging.getLogger(__name__)

        # 初始化模糊控制器
        self._setup_fuzzy_controller()

    def _normalize_input(self, error, error_dot):
        """归一化输入变量"""
        # 调整归一化范围
        error_norm = np.clip(error / 90.0, -1, 1)  # 将航向误差范围从[-180, 180]映射到[-1, 1]
        error_dot_norm = np.clip(error_dot / 5.0, -1, 1)  # 将误差变化率范围从[-10, 10]映射到[-1, 1]
        return error_norm, error_dot_norm

    def _setup_fuzzy_controller(self):
        """设置模糊控制器"""
        # 创建模糊控制系统
        self.fuzzy_simulator = ctrl.ControlSystem()

        # 定义输入变量
        error = ctrl.Antecedent(np.linspace(-1, 1, 7), 'error')
        error_dot = ctrl.Antecedent(np.linspace(-1, 1, 7), 'error_dot')
        output = ctrl.Consequent(np.linspace(-1, 1, 7), 'output')

        # 定义模糊集
        error.automf(names=['NB', 'NM', 'NS', 'ZO', 'PS', 'PM', 'PB'])
        error_dot.automf(names=['NB', 'NM', 'NS', 'ZO', 'PS', 'PM', 'PB'])
        output.automf(names=['NB', 'NM', 'NS', 'ZO', 'PS', 'PM', 'PB'])

        # 定义完整的模糊规则集
        rule_matrix = [
            # error: NB
            ctrl.Rule(error['NB'] & error_dot['NB'], output['NB']),
            ctrl.Rule(error['NB'] & error_dot['NM'], output['NB']),
            ctrl.Rule(error['NB'] & error_dot['NS'], output['NM']),
            ctrl.Rule(error['NB'] & error_dot['ZO'], output['NM']),
            ctrl.Rule(error['NB'] & error_dot['PS'], output['NS']),
            ctrl.Rule(error['NB'] & error_dot['PM'], output['ZO']),
            ctrl.Rule(error['NB'] & error_dot['PB'], output['PS']),

            # error: NM
            ctrl.Rule(error['NM'] & error_dot['NB'], output['NB']),
            ctrl.Rule(error['NM'] & error_dot['NM'], output['NM']),
            ctrl.Rule(error['NM'] & error_dot['NS'], output['NM']),
            ctrl.Rule(error['NM'] & error_dot['ZO'], output['NS']),
            ctrl.Rule(error['NM'] & error_dot['PS'], output['ZO']),
            ctrl.Rule(error['NM'] & error_dot['PM'], output['PS']),
            ctrl.Rule(error['NM'] & error_dot['PB'], output['PM']),

            # error: NS
            ctrl.Rule(error['NS'] & error_dot['NB'], output['NM']),
            ctrl.Rule(error['NS'] & error_dot['NM'], output['NM']),
            ctrl.Rule(error['NS'] & error_dot['NS'], output['NS']),
            ctrl.Rule(error['NS'] & error_dot['ZO'], output['NS']),
            ctrl.Rule(error['NS'] & error_dot['PS'], output['ZO']),
            ctrl.Rule(error['NS'] & error_dot['PM'], output['PS']),
            ctrl.Rule(error['NS'] & error_dot['PB'], output['PM']),

            # error: ZO
            ctrl.Rule(error['ZO'] & error_dot['NB'], output['NM']),
            ctrl.Rule(error['ZO'] & error_dot['NM'], output['NS']),
            ctrl.Rule(error['ZO'] & error_dot['NS'], output['NS']),
            ctrl.Rule(error['ZO'] & error_dot['ZO'], output['ZO']),
            ctrl.Rule(error['ZO'] & error_dot['PS'], output['PS']),
            ctrl.Rule(error['ZO'] & error_dot['PM'], output['PS']),
            ctrl.Rule(error['ZO'] & error_dot['PB'], output['PM']),

            # error: PS
            ctrl.Rule(error['PS'] & error_dot['NB'], output['NM']),
            ctrl.Rule(error['PS'] & error_dot['NM'], output['NS']),
            ctrl.Rule(error['PS'] & error_dot['NS'], output['ZO']),
            ctrl.Rule(error['PS'] & error_dot['ZO'], output['PS']),
            ctrl.Rule(error['PS'] & error_dot['PS'], output['PS']),
            ctrl.Rule(error['PS'] & error_dot['PM'], output['PM']),
            ctrl.Rule(error['PS'] & error_dot['PB'], output['PB']),

            # error: PM
            ctrl.Rule(error['PM'] & error_dot['NB'], output['NS']),
            ctrl.Rule(error['PM'] & error_dot['NM'], output['ZO']),
            ctrl.Rule(error['PM'] & error_dot['NS'], output['PS']),
            ctrl.Rule(error['PM'] & error_dot['ZO'], output['PM']),
            ctrl.Rule(error['PM'] & error_dot['PS'], output['PM']),
            ctrl.Rule(error['PM'] & error_dot['PM'], output['PB']),
            ctrl.Rule(error['PM'] & error_dot['PB'], output['PB']),

            # error: PB
            ctrl.Rule(error['PB'] & error_dot['NB'], output['ZO']),
            ctrl.Rule(error['PB'] & error_dot['NM'], output['PS']),
            ctrl.Rule(error['PB'] & error_dot['NS'], output['PM']),
            ctrl.Rule(error['PB'] & error_dot['ZO'], output['PB']),
            ctrl.Rule(error['PB'] & error_dot['PS'], output['PB']),
            ctrl.Rule(error['PB'] & error_dot['PM'], output['PB']),
            ctrl.Rule(error['PB'] & error_dot['PB'], output['PB'])
        ]

        # 添加规则到控制系统
        for rule in rule_matrix:
            self.fuzzy_simulator.addrule(rule)

        # 创建控制系统仿真器
        self.fuzzy_ctrl = ctrl.ControlSystemSimulation(self.fuzzy_simulator)

    def _update_pid_parameters(self, error, error_dot):
        """更新PID参数"""
        try:
            # 归一化输入
            error_norm, error_dot_norm = self._normalize_input(error, error_dot)

            # 设置模糊控制器输入
            self.fuzzy_ctrl.input['error'] = error_norm
            self.fuzzy_ctrl.input['error_dot'] = error_dot_norm

            # 计算模糊控制器输出
            self.fuzzy_ctrl.compute()

            # 获取输出并更新PID参数
            fuzzy_output = self.fuzzy_ctrl.output['output']

            # 调整增益系数
            self.kp = self.kp_base * (1 + 0.5 * fuzzy_output)  # 增大基础增益
            self.ki = self.ki_base * (1 + 0.5 * fuzzy_output)
            self.kd = self.kd_base * (1 + 0.5 * fuzzy_output)

            # 记录调试信息
            self.logger.debug(f"归一化误差: {error_norm:.4f}")
            self.logger.debug(f"归一化误差变化率: {error_dot_norm:.4f}")
            self.logger.debug(f"模糊输出: {fuzzy_output:.4f}")
            self.logger.debug(f"更新后的PID参数 - Kp: {self.kp:.4f}, Ki: {self.ki:.4f}, Kd: {self.kd:.4f}")

        except Exception as e:
            self.logger.error(f"更新PID参数时出错: {str(e)}")
            raise

    def compute(self, setpoint, feedback, dt=None):
        """计算控制输出

        Args:
            setpoint (float): 目标值
            feedback (float): 反馈值
            dt (float, optional): 时间步长，如果未提供则使用初始化时的dt

        Returns:
            float: 控制输出值
        """
        try:
            # 使用提供的dt或默认值
            current_dt = dt if dt is not None else self.dt

            # 计算误差 - 对于航向控制，需要特殊处理角度差
            if self.controller_type == 'heading':
                # 标准化航向误差到[-180, 180]范围
                error = setpoint - feedback
                while error > 180:
                    error -= 360
                while error < -180:
                    error += 360
            else:
                error = setpoint - feedback

            # 计算误差变化率
            error_dot = (error - self.prev_error) / current_dt

            # 使用固定PID参数，不使用模糊逻辑更新
            # 避免使用可能导致崩溃的模糊控制器
            # self._update_pid_parameters(error, error_dot)

            # 使用基础PID参数
            self.kp = self.kp_base
            self.ki = self.ki_base
            self.kd = self.kd_base

            # 计算积分项 - 添加防饱和措施
            if abs(error) < 30:  # 只在误差较小时累积积分项
                self.integral += error * current_dt
            else:
                # 误差较大时，减小积分项以防止过冲
                self.integral *= 0.9

            # 限制积分项大小
            self.integral = np.clip(self.integral, -self.integral_limit, self.integral_limit)

            # 计算控制输出
            p_term = self.kp * error
            i_term = self.ki * self.integral
            d_term = self.kd * error_dot

            # 对于航向控制，添加特殊处理以防止旋回
            if self.controller_type == 'heading':
                # 如果误差变化率过大（表示船舶正在快速转向），减小比例项和积分项
                if abs(error_dot) > 5:
                    p_term *= 0.7
                    i_term *= 0.5

                # 如果误差和误差变化率符号相反（表示船舶正在接近目标但可能过冲），增加微分项
                if error * error_dot < 0:
                    d_term *= 1.5

            output = p_term + i_term + d_term

            # 输出平滑处理 - 增加平滑系数以减少控制输出的突变
            self.alpha = 0.7  # 增加平滑系数
            output = self.alpha * output + (1 - self.alpha) * self.prev_output

            # 对于航向控制，限制最大舵角变化率
            if self.controller_type == 'heading':
                max_change = 5.0  # 每步最大舵角变化（度）
                if abs(output - self.prev_output) > max_change:
                    if output > self.prev_output:
                        output = self.prev_output + max_change
                    else:
                        output = self.prev_output - max_change

            # 更新状态
            self.prev_error = error
            self.prev_output = output

            # 记录调试信息
            self.logger.debug(f"设定值: {setpoint:.3f}, 反馈值: {feedback:.3f}")
            self.logger.debug(f"误差: {error:.3f}, 误差变化率: {error_dot:.3f}")
            self.logger.debug(f"P项: {p_term:.3f}, I项: {i_term:.3f}, D项: {d_term:.3f}")
            self.logger.debug(f"控制输出: {output:.3f}")

            return output

        except Exception as e:
            self.logger.error(f"计算控制输出时出错: {str(e)}")
            return 0.0

    def reset(self):
        """重置控制器状态"""
        self.integral = 0.0
        self.prev_error = 0.0
        self.prev_output = 0.0
        # 重置PID参数
        self.kp = self.kp_base
        self.ki = self.ki_base
        self.kd = self.kd_base
        # 重置模糊控制器
        self.fuzzy_ctrl = ctrl.ControlSystemSimulation(self.fuzzy_simulator)

    # 删除未使用的方法，避免混淆