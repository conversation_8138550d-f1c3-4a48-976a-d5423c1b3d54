# -*- coding: utf-8 -*-
"""
MMG模型单元测试
"""
import pytest
import numpy as np
from ship_collision_avoidance.models.mmg_model import MMGModel
from ship_collision_avoidance.models.mmg_coefficients import MMGCoefficients
from ship_collision_avoidance.config import SHIP_CONFIG
from ship_collision_avoidance.tests.test_visualization import TestVisualizer
import matplotlib.pyplot as plt

# 极限工况测试场景
EXTREME_SCENARIO = {
    'name': 'extreme_condition',
    'description': '极限工况测试',
    'own_ship': {
        'initial_state': {
            'x': 0.0, 'y': 0.0, 'psi': 0.0,
            'u': 8.0, 'v': 0.0, 'r': 0.0
        }
    },
    'simulation_config': {
        'duration': 1800.0,
        'time_step': 0.1,
        'safe_distance': 400.0,
        'environmental_conditions': {
            'wind_speed': 15.0,
            'wind_direction': 45.0,
            'current_speed': 1.0,
            'current_direction': 90.0
        }
    }
}

class TestMMGModel:
    """MMG模型测试类"""
    
    @pytest.fixture(autouse=True)
    def setup_and_teardown(self):
        """设置和清理测试环境"""
        yield
        plt.close('all')  # 清理所有图形
    
    @pytest.fixture
    def visualizer(self):
        """创建可视化器实例"""
        vis = TestVisualizer()
        yield vis
        vis.show()
        plt.close('all')
    
    def test_all_with_visualization(self, visualizer):
        """运行所有测试并可视化结果"""
        # 初始化测试
        model = MMGModel()
        visualizer.plot_initialization_test(model)
        
        # 力计算测试
        model.u = 5.0
        X, Y, N = model.calculate_forces(np.deg2rad(10), 60)
        visualizer.plot_force_calculation(X, Y, N)
        
        # 状态更新测试
        states = {'x': [], 'y': []}
        model.x = 100.0
        model.y = 200.0
        model.psi = np.deg2rad(45)
        model.u = 5.0
        
        for _ in range(10):
            states['x'].append(model.x)
            states['y'].append(model.y)
            model.update_state(1000.0, 0.0, 0.0, 0.1)
        visualizer.plot_state_update(states)
        
        # 转向运动测试
        model = MMGModel()
        turning_states = {'x': [], 'y': []}
        model.u = 5.0
        
        for _ in range(100):
            turning_states['x'].append(model.x)
            turning_states['y'].append(model.y)
            X, Y, N = model.calculate_forces(np.deg2rad(20), 60)
            model.update_state(X, Y, N, 0.1)
        visualizer.plot_turning_motion(turning_states)
        
        # 直线运动测试
        model = MMGModel()
        straight_states = {'x': [], 'y': []}
        model.u = 5.0
        
        for _ in range(100):
            straight_states['x'].append(model.x)
            straight_states['y'].append(model.y)
            X, Y, N = model.calculate_forces(0.0, 60)
            model.update_state(X, Y, N, 0.1)
        visualizer.plot_straight_motion(straight_states)
        
        # 减速性能测试
        model = MMGModel()
        speeds = []
        model.u = 5.0
        
        for _ in range(100):
            speeds.append(model.u)
            X, Y, N = model.calculate_forces(0.0, 0)
            model.update_state(X, Y, N, 0.1)
        visualizer.plot_deceleration(speeds)
        
        # 无效输入测试
        visualizer.plot_invalid_inputs(np.rad2deg(model.max_rudder_angle), model.max_rps)
        
        # 水动力系数测试
        coeffs = MMGCoefficients(SHIP_CONFIG)
        visualizer.plot_coefficients(coeffs)
        
        # 显示所有测试结果
        visualizer.show()
        
        # 验证测试结果
        assert model.L == SHIP_CONFIG['L']
        assert isinstance(X, float)
        assert model.x > 100.0
        assert model.psi > 0.0
        assert abs(model.y) < 1.0
        assert model.u < 5.0
        assert hasattr(coeffs, 'Xvv')

    def test_invalid_inputs(self):
        """测试无效输入处理"""
        model = MMGModel()
        
        with pytest.raises(ValueError):
            model.calculate_forces(np.deg2rad(50), 60)
        
        with pytest.raises(ValueError):
            model.calculate_forces(0.0, -10)

    @pytest.mark.parametrize("test_case", [
        {"rudder_angle": 10, "rps": 60, "duration": 100},
        {"rudder_angle": 20, "rps": 80, "duration": 150},
        {"rudder_angle": 30, "rps": 40, "duration": 200}
    ])
    def test_turning_performance(self, test_case):
        """转向性能参数化测试"""
        model = MMGModel()
        model.u = 5.0
        states = {'x': [], 'y': []}
        
        for _ in range(test_case["duration"]):
            states['x'].append(model.x)
            states['y'].append(model.y)
            X, Y, N = model.calculate_forces(
                np.deg2rad(test_case["rudder_angle"]), 
                test_case["rps"])
            model.update_state(X, Y, N, 0.1)
        
        # 验证转向性能
        final_heading = np.rad2deg(model.psi)
        turning_radius = np.sqrt(states['x'][-1]**2 + states['y'][-1]**2)
        
        assert 0 < final_heading < 360
        assert turning_radius > 0

def test_mmg_model_initialization():
    """测试MMG模型初始化"""
    model = MMGModel()
    
    assert model.L == SHIP_CONFIG['L']
    assert model.B == SHIP_CONFIG['B']
    assert model.T == SHIP_CONFIG['T']
    assert model.Cb == SHIP_CONFIG['Cb']
    
    # 检查初始状态
    assert model.u == 0.0
    assert model.v == 0.0
    assert model.r == 0.0
    assert model.x == 0.0
    assert model.y == 0.0
    assert model.psi == 0.0

def test_force_calculation():
    """测试力的计算"""
    model = MMGModel()
    
    # 设置初始速度
    model.u = 5.0  # 5 m/s
    
    # 计算受力
    X, Y, N = model.calculate_forces(np.deg2rad(10), 60)  # 10度舵角，60 RPS
    
    # 检查力的合理性
    assert isinstance(X, float)
    assert isinstance(Y, float)
    assert isinstance(N, float)
    
    # 检查方向是否正确
    assert Y != 0  # 有舵角时应该产生横向力
    assert N != 0  # 有舵角时应该产生回转力矩

def test_state_update():
    """测试状态更新"""
    model = MMGModel()
    
    # 设置初始状态
    initial_x = 100.0
    initial_y = 200.0
    initial_psi = np.deg2rad(45)
    initial_u = 5.0
    
    model.x = initial_x
    model.y = initial_y
    model.psi = initial_psi
    model.u = initial_u
    
    # 更新状态
    dt = 0.1
    X = 1000.0  # 前进推力
    Y = 0.0     # 无横向力
    N = 0.0     # 无回转力矩
    
    model.update_state(X, Y, N, dt)
    
    # 检查位置更新
    assert model.x > initial_x  # 应该向前移动
    assert model.y > initial_y  # 45度航向应该向上移动
    assert model.psi == initial_psi  # 无回转力矩时航向应保持不变
    assert model.u > initial_u  # 有推力时速度应增加

def test_turning_motion():
    """测试转向运动"""
    model = MMGModel()
    
    # 设置初始状态
    model.u = 5.0  # 5 m/s
    initial_psi = 0.0
    
    # 模拟转向
    dt = 0.1
    steps = 100
    
    for _ in range(steps):
        X, Y, N = model.calculate_forces(np.deg2rad(20), 60)  # 20度舵角
        model.update_state(X, Y, N, dt)
    
    # 检查是否转向
    assert model.psi > initial_psi  # 应该向右转向
    assert model.r > 0  # 应该有正的角速度

def test_straight_motion():
    """测试直线运动"""
    model = MMGModel()
    
    # 设置初始状态
    initial_x = 0.0
    initial_y = 0.0
    model.u = 5.0  # 5 m/s
    
    # 模拟直线运动
    dt = 0.1
    steps = 100
    
    for _ in range(steps):
        X, Y, N = model.calculate_forces(0.0, 60)  # 无舵角
        model.update_state(X, Y, N, dt)
    
    # 检查直线运动
    assert abs(model.y - initial_y) < 1.0  # 横向偏移应该很小
    assert model.x > initial_x  # 应该向前移动
    assert abs(model.psi) < 0.01  # 航向变化应该很小

def test_deceleration():
    """测试减速性能"""
    model = MMGModel()
    
    # 设置初始状态
    model.u = 5.0  # 5 m/s
    initial_u = model.u
    
    # 模拟减速
    dt = 0.1
    steps = 100
    
    for _ in range(steps):
        X, Y, N = model.calculate_forces(0.0, 0)  # 停止推进
        model.update_state(X, Y, N, dt)
    
    # 检查减速效果
    assert model.u < initial_u  # 速度应该降低
    assert model.u > 0  # 不应该完全停止（水阻）

def test_mmg_coefficients():
    """测试水动力系数计算"""
    coeffs = MMGCoefficients(SHIP_CONFIG)
    
    # 检查基本系数
    assert hasattr(coeffs, 'Xvv')
    assert hasattr(coeffs, 'Yv')
    assert hasattr(coeffs, 'Nr')
    
    # 检查系数符号
    assert coeffs.Xvv < 0  # 横向运动阻力应为负
    assert coeffs.Yv < 0  # 横向力系数应为负
    assert coeffs.Nr < 0  # 回转阻尼应为负

def calculate_performance_metrics(self):
    """计算性能指标"""
    metrics = {
        'simulation_duration': time_series['time'].max(),
        'min_safety_distance': self._calculate_min_safety_distance(),
        'path_deviation': self._calculate_path_deviation(own_ship_positions),
        'control_effort': self._calculate_control_effort(),
        'risk_statistics': self._calculate_risk_statistics(),
        'collision_avoidance_success': self._evaluate_collision_avoidance()
    }

def plot_state_update(self, states):
    ax = self.fig.add_subplot(self.gs[0, 2])
    ax.plot(states['x'], states['y'], 'b-', label='轨迹')
    ax.set_title('状态更新测试')
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.grid(True)
    ax.legend()