# -*- coding: utf-8 -*-
"""
碰撞危险度评估模块
"""
import numpy as np
from typing import Dict, List, Tuple
from ..config import COLLISION_AVOIDANCE_CONFIG
import logging

class CollisionRiskAssessment:
    def __init__(self):
        """初始化碰撞危险度评估器"""
        # 从配置文件加载参数
        self.w_dcpa = COLLISION_AVOIDANCE_CONFIG['risk_weights']['dcpa']       # DCPA权重
        self.w_tcpa = COLLISION_AVOIDANCE_CONFIG['risk_weights']['tcpa']       # TCPA权重
        self.w_dist = COLLISION_AVOIDANCE_CONFIG['risk_weights']['dist']      # 距离权重
        self.w_velocity = 1 - (self.w_dcpa + self.w_tcpa + self.w_dist)    # 相对速度权重

        # 危险阈值
        self.safe_distance = COLLISION_AVOIDANCE_CONFIG['safe_distance']  # 安全距离(m)
        self.min_dcpa = COLLISION_AVOIDANCE_CONFIG['dcpa_threshold']     # 最小DCPA(m)
        self.min_tcpa = COLLISION_AVOIDANCE_CONFIG['tcpa_threshold']     # 最小TCPA(s)
        self.max_tcpa = COLLISION_AVOIDANCE_CONFIG['tcpa_threshold'] * 3  # 最大TCPA(s)

        # 初始化日志记录器
        self.logger = logging.getLogger(__name__)
        self.logger.info("碰撞风险评估器初始化完成")

    def calculate_dcpa_tcpa(self, own_ship: Dict, target_ship: Dict) -> Tuple[float, float]:
        """计算DCPA（最近会遇距离）和TCPA（最近会遇时间）

        Args:
            own_ship (Dict): 本船状态
            target_ship (Dict): 目标船状态

        Returns:
            Tuple[float, float]: (DCPA, TCPA)
        """
        # 相对位置
        dx = target_ship['x'] - own_ship['x']
        dy = target_ship['y'] - own_ship['y']

        # 相对速度
        dvx = (target_ship['u'] * np.cos(target_ship['psi']) -
               own_ship['u'] * np.cos(own_ship['psi']))
        dvy = (target_ship['u'] * np.sin(target_ship['psi']) -
               own_ship['u'] * np.sin(own_ship['psi']))

        # 计算TCPA和DCPA
        v_rel = np.sqrt(dvx**2 + dvy**2)
        tcpa = -(dx*dvx + dy*dvy) / (v_rel**2)
        dcpa = abs(dx*dvy - dy*dvx) / v_rel

        return dcpa, tcpa

    def calculate_ship_domain(self, ship_type: str, length: float) -> float:
        """计算船舶领域半径

        Args:
            ship_type (str): 船舶类型
            length (float): 船长

        Returns:
            float: 船舶领域半径
        """
        # 根据船舶类型和尺寸确定领域系数
        domain_coefficients = {
            'cargo': 2.0,
            'tanker': 2.5,
            'passenger': 3.0,
            'fishing': 1.5
        }

        coef = domain_coefficients.get(ship_type, 2.0)
        return coef * length

    def calculate_maneuverability_factor(self, ship: Dict) -> float:
        """计算船舶操纵性能因子

        Args:
            ship (Dict): 船舶状态和参数

        Returns:
            float: 操纵性能因子 [0,1]
        """
        # 考虑转向性能
        turning_ability = min(1.0, ship.get('rudder_angle_max', 35) / 35.0)

        # 考虑加减速性能
        speed_range = ship.get('max_speed', 20) - ship.get('min_speed', 0)
        speed_ability = min(1.0, speed_range / 20.0)

        # 综合评估
        return 0.6 * turning_ability + 0.4 * speed_ability

    def calculate_encounter_situation(self, own_ship: Dict, target_ship: Dict) -> Dict:
        """判断会遇类型和相对位置关系

        Args:
            own_ship (Dict): 本船状态
            target_ship (Dict): 目标船状态

        Returns:
            Dict: 包含会遇类型和相对位置信息的字典
                - type: 会遇类型 ('overtaking', 'head_on', 'crossing')
                - relative_bearing: 相对方位角（度）
                - is_give_way: 本船是否为让路船
                - target_on_starboard: 目标船是否在本船右舷
        """
        # 计算相对方位
        dx = target_ship['x'] - own_ship['x']
        dy = target_ship['y'] - own_ship['y']
        relative_bearing = np.arctan2(dy, dx) - own_ship['psi']
        relative_bearing_deg = np.rad2deg(relative_bearing) % 360

        # 计算相对航向
        relative_course = target_ship['psi'] - own_ship['psi']
        relative_course_deg = np.rad2deg(relative_course) % 360

        # 判断目标船是否在本船右舷（0-112.5度或247.5-360度）
        target_on_starboard = (0 <= relative_bearing_deg <= 112.5) or (247.5 <= relative_bearing_deg <= 360)

        # 判断会遇类型
        encounter_type = ""
        is_give_way = False

        # 追越局面：本船从后方接近目标船
        if abs(relative_course_deg) < 45:
            encounter_type = 'overtaking'
            is_give_way = True  # 追越船为让路船

        # 对遇局面：船舶在相反或接近相反的航向上会遇
        elif abs(relative_course_deg - 180) < 45:
            encounter_type = 'head_on'
            is_give_way = True  # 对遇局面双方都应避让，向右转向

        # 交叉相遇局面
        else:
            encounter_type = 'crossing'

            # 根据国际海上避碰规则第15条，当另一船在本船右舷时，本船为让路船
            is_give_way = target_on_starboard

            # 如果目标船在本船左舷，且本船在目标船右舷，则目标船为让路船
            # 这种情况下本船为直航船，应保持航向和航速

        return {
            'type': encounter_type,
            'relative_bearing': relative_bearing_deg,
            'is_give_way': is_give_way,
            'target_on_starboard': target_on_starboard
        }

    def calculate_risk_index(self, own_ship: Dict, target_ship: Dict) -> float:
        """计算综合碰撞危险度指标

        Args:
            own_ship (Dict): 本船状态和参数
            target_ship (Dict): 目标船状态和参数

        Returns:
            float: 危险度指标 [0,1]
        """
        try:
            # 计算DCPA和TCPA
            dcpa, tcpa = self.calculate_dcpa_tcpa(own_ship, target_ship)

            # 计算当前距离
            distance = np.sqrt((target_ship['x'] - own_ship['x'])**2 +
                             (target_ship['y'] - own_ship['y'])**2)

            # 计算相对速度
            v_rel = np.sqrt((target_ship['u'] * np.cos(target_ship['psi']) -
                            own_ship['u'] * np.cos(own_ship['psi']))**2 +
                           (target_ship['u'] * np.sin(target_ship['psi']) -
                            own_ship['u'] * np.sin(own_ship['psi']))**2)

            # 归一化各个指标
            distance_risk = max(0, 1 - distance / self.safe_distance)
            dcpa_risk = max(0, 1 - dcpa / self.min_dcpa)

            if tcpa < 0 or tcpa > self.max_tcpa:
                tcpa_risk = 0
            else:
                tcpa_risk = max(0, 1 - abs(tcpa - self.min_tcpa) /
                              (self.max_tcpa - self.min_tcpa))

            velocity_risk = min(1, v_rel / 10.0)  # 假设10m/s为危险相对速度

            # 考虑会遇类型和相对位置
            encounter_situation = self.calculate_encounter_situation(own_ship, target_ship)
            encounter_type = encounter_situation['type']
            is_give_way = encounter_situation['is_give_way']
            target_on_starboard = encounter_situation['target_on_starboard']

            # 根据会遇类型和相对位置调整风险系数
            encounter_coefficients = {
                'head_on': 1.2,  # 对遇局面风险较高
                'crossing': 1.0,
                'overtaking': 0.8
            }

            # 基础风险系数
            encounter_coef = encounter_coefficients[encounter_type]

            # 如果是交叉相遇且目标船在右舷（本船为让路船），风险系数增加
            if encounter_type == 'crossing' and target_on_starboard:
                encounter_coef *= 1.2  # 增加20%的风险

            # 计算操纵性能因子
            maneuver_factor = self.calculate_maneuverability_factor(own_ship)

            # 综合危险度指标
            risk_index = (self.w_dist * distance_risk +
                         self.w_dcpa * dcpa_risk +
                         self.w_tcpa * tcpa_risk +
                         self.w_velocity * velocity_risk)

            # 考虑会遇类型和操纵性能的影响
            risk_index *= encounter_coef
            risk_index *= (2 - maneuver_factor)  # 操纵性能越好，风险越低

            self.logger.debug(f"计算得到的风险指数: {risk_index:.3f}")
            return min(1.0, max(0.0, risk_index))

        except Exception as e:
            self.logger.error(f"计算风险指数时出错: {str(e)}")
            return 1.0  # 出错时返回最高风险

    def evaluate_multi_ship_risk(self, own_ship: Dict, target_ships: List[Dict]) -> Dict:
        """评估多船碰撞风险

        Args:
            own_ship (Dict): 本船状态和参数
            target_ships (List[Dict]): 目标船列表

        Returns:
            Dict: 风险评估结果
        """
        risk_results = []
        total_risk = 0
        highest_risk = 0
        most_dangerous_ship = None

        for i, target_ship in enumerate(target_ships):
            # 只计算风险值，不存储其他可能导致格式化错误的对象
            risk = self.calculate_risk_index(own_ship, target_ship)
            risk_results.append(risk)

            total_risk += risk
            if risk > highest_risk:
                highest_risk = risk
                most_dangerous_ship = i  # 存储索引而不是对象

        return {
            'individual_risks': risk_results,
            'total_risk': total_risk / len(target_ships) if target_ships else 0,
            'highest_risk': highest_risk,
            'most_dangerous_ship': most_dangerous_ship
        }

    def analyze_safety_performance(self) -> Dict:
        safety_metrics = []

        for sim_id, data in self.simulations.items():
            metrics = data['metadata']['performance_metrics']
            safety_metrics.append({
                'min_safety_distance': metrics['min_safety_distance'],
                'max_risk': metrics['risk_statistics']['max_risk'],
                'time_in_high_risk': metrics['risk_statistics']['time_in_high_risk'],
                'collision_avoided': metrics['collision_avoidance_success']['success']
            })