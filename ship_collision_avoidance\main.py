# -*- coding: utf-8 -*-
"""
船舶避碰系统主程序
"""
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Ellipse, Circle
from ship_collision_avoidance.models.mmg_model import MMGModel
from ship_collision_avoidance.controllers.fuzzy_pid_controller import FuzzyPIDController
from ship_collision_avoidance.planners.velocity_obstacle import VelocityObstacle
from ship_collision_avoidance.collision_risk.risk_assessment import CollisionRiskAssessment
from ship_collision_avoidance.visualization.visualizer import CollisionAvoidanceVisualizer
from ship_collision_avoidance.config import SIMULATION_CONFIG, SHIP_CONFIG

def run_simulation():
    """运行避碰仿真"""
    # 初始化系统组件
    own_ship = MMGModel()
    heading_controller = FuzzyPIDController('heading')
    speed_controller = FuzzyPIDController('speed')
    collision_avoider = VelocityObstacle()
    risk_assessor = CollisionRiskAssessment()
    visualizer = CollisionAvoidanceVisualizer()

    # 仿真参数
    dt = SIMULATION_CONFIG['dt']
    total_time = SIMULATION_CONFIG['total_time']
    time_steps = int(total_time / dt)

    # 设置可视化范围
    visualizer.setup_plot((-2000, 2000), (-2000, 2000))

    # 初始化本船状态
    own_ship.x = 0.0
    own_ship.y = 0.0
    own_ship.psi = np.deg2rad(30)  # 30度航向
    own_ship.u = 7.0  # 7 m/s（约13.6节）

    # 强制重置角速度和横向速度为0
    own_ship.v = 0.0  # 横向速度
    own_ship.r = 0.0  # 角速度

    # 打印初始状态
    print("\n船舶初始状态:")
    print(f"  位置: ({own_ship.x:.1f}, {own_ship.y:.1f})")
    print(f"  航向: {np.rad2deg(own_ship.psi):.1f}°")
    print(f"  纵向速度: {own_ship.u:.1f} m/s")
    print(f"  横向速度: {own_ship.v:.1f} m/s")
    print(f"  角速度: {np.rad2deg(own_ship.r):.4f}°/s")
    print(f"  位置: ({own_ship.x:.1f}, {own_ship.y:.1f})")
    print(f"  航向: {np.rad2deg(own_ship.psi):.1f}°")
    print(f"  纵向速度: {own_ship.u:.1f} m/s")
    print(f"  横向速度: {own_ship.v:.1f} m/s")
    print(f"  角速度: {np.rad2deg(own_ship.r):.4f}°/s")

    # 记录初始航向和航速作为目标值
    target_course = np.rad2deg(own_ship.psi)
    target_speed = own_ship.u

    # 避让状态标志
    is_avoiding = False
    avoidance_mode = "none"  # 避让模式：none, course_change, speed_change, combined, emergency

    # 风险阈值定义 - 根据国际海上避碰规则
    general_risk_threshold = 0.2    # 降低一般风险阈值，更早采取避让行动
    urgent_risk_threshold = 0.4     # 降低紧迫局面阈值
    emergency_risk_threshold = 0.6  # 降低紧迫危险阈值
    safe_threshold = 0.1            # 降低安全阈值

    # 初始化目标船舶（创建危险的交叉相遇场景）
    target_ships = [
        {
            'id': 'Target1',
            'x': 1500.0,  # 在本船右前方
            'y': 1000.0,
            'psi': np.deg2rad(225),  # 朝本船方向
            'u': 6.0,  # 6 m/s
            'v': 0.0,
            'r': 0.0,
            'length': 150.0,
            'width': 20.0,
            'type': 'cargo'
        },
        {
            'id': 'Target2',
            'x': -1000.0,  # 在本船左前方
            'y': 1500.0,
            'psi': np.deg2rad(135),  # 朝本船方向
            'u': 5.5,  # 5.5 m/s
            'v': 0.0,
            'r': 0.0,
            'length': 120.0,
            'width': 18.0,
            'type': 'tanker'
        }
    ]

    # 记录轨迹
    own_trajectory = []
    target_trajectories = {ship['id']: [] for ship in target_ships}

    # 记录风险指标
    risk_history = []

    # 主循环
    for t in range(time_steps):
        # 计算当前时间
        sim_time = t * dt

        # 获取当前状态
        own_state = own_ship.get_state()

        # 检查并修复异常的角速度和横向速度
        if abs(np.rad2deg(own_ship.r)) > 1.0:  # 角速度大于1度/秒认为异常
            print(f"警告：检测到异常角速度 {np.rad2deg(own_ship.r):.3f}°/s，强制重置为0")
            own_ship.r = 0.0  # 强制重置角速度

        if abs(own_ship.v) > 0.2 * own_ship.u:  # 横向速度大于纵向速度的20%认为异常
            print(f"警告：检测到异常横向速度 {own_ship.v:.3f} m/s，强制重置为0")
            own_ship.v = 0.0  # 强制重置横向速度

        # 更新状态字典
        own_state = own_ship.get_state()  # 重新获取状态，确保使用修正后的值
        own_state.update({
            'length': SHIP_CONFIG['L'],
            'width': SHIP_CONFIG['B'],
            'type': 'cargo'
        })
        own_trajectory.append((own_state['x'], own_state['y']))

        # 更新目标船位置（考虑航向）
        for target_ship in target_ships:
            # 更新位置
            target_ship['x'] += target_ship['u'] * np.cos(target_ship['psi']) * dt
            target_ship['y'] += target_ship['u'] * np.sin(target_ship['psi']) * dt
            target_trajectories[target_ship['id']].append(
                (target_ship['x'], target_ship['y']))

        # 评估碰撞风险
        risk_result = risk_assessor.evaluate_multi_ship_risk(own_state, target_ships)
        risk_history.append(risk_result['highest_risk'])

        # 避让决策 - 根据国际海上避碰规则采取不同的避让策略
        current_risk = risk_result['highest_risk']
        current_speed = np.sqrt(own_state['u']**2 + own_state['v']**2)

        # 获取会遇类型和相对位置信息（如果有目标船）
        encounter_info = {
            "type": "unknown",
            "is_give_way": False,
            "target_on_starboard": False,
            "relative_bearing": 0.0
        }

        # 提取会遇类型，用于后续处理
        encounter_type = "unknown"
        is_give_way = False
        target_on_starboard = False

        if target_ships:
            # 获取最危险目标船
            most_dangerous_idx = risk_result.get('most_dangerous_ship', 0)
            if isinstance(most_dangerous_idx, int) and most_dangerous_idx < len(target_ships):
                target_ship = target_ships[most_dangerous_idx]

                # 使用风险评估器获取详细的会遇信息
                encounter_info = risk_assessor.calculate_encounter_situation(own_state, target_ship)

                # 提取会遇类型和相对位置信息
                encounter_type = encounter_info['type']
                is_give_way = encounter_info['is_give_way']
                target_on_starboard = encounter_info['target_on_starboard']

                # 打印会遇类型和相对位置信息，用于调试
                if t % 20 == 0:
                    print(f"\n=== 时间: {sim_time:.1f}s ===")
                    print(f"会遇类型: {encounter_type}")
                    print(f"目标船位置: {'右舷' if target_on_starboard else '左舷'}")
                    print(f"本船角色: {'让路船' if is_give_way else '直航船'}")
                    print(f"相对方位: {encounter_info['relative_bearing']:.1f}°")
                    print(f"当前风险指数: {current_risk:.3f}")
                    print(f"一般风险阈值: {general_risk_threshold}")
                    print(f"紧迫风险阈值: {urgent_risk_threshold}")
                    print(f"紧急风险阈值: {emergency_risk_threshold}")

                    # 计算距离
                    distance = np.sqrt((target_ship['x'] - own_state['x'])**2 +
                                     (target_ship['y'] - own_state['y'])**2)
                    print(f"距离: {distance:.1f}m")

                    # 计算DCPA和TCPA
                    dcpa, tcpa = risk_assessor.calculate_dcpa_tcpa(own_state, target_ship)
                    print(f"DCPA: {dcpa:.1f}m, TCPA: {tcpa:.1f}s")

        # 强制避让逻辑：对于对遇状态，无论风险指数如何，都要采取避让行动
        force_avoidance = False
        if encounter_type == "head_on":
            # 计算距离
            distance = np.sqrt((target_ship['x'] - own_state['x'])**2 +
                             (target_ship['y'] - own_state['y'])**2)
            # 如果距离小于1500米，强制避让
            if distance < 1500:
                force_avoidance = True
                if t % 20 == 0:
                    print(f"🚨 强制避让：对遇状态，距离{distance:.1f}m < 1500m，强制右转避让！")

        # 根据风险程度采取不同的避让策略
        if force_avoidance or current_risk >= emergency_risk_threshold:
            # 紧迫危险 - 采用一切可用手段进行避让
            is_avoiding = True
            avoidance_mode = "emergency"

            # 计算避碰决策 - 同时改变航向和速度
            safe_speed, safe_course_deg = collision_avoider.find_safe_velocity(own_state, target_ships)

            # 如果风险极高且速度较快，可以考虑急速减速
            if current_speed > 10:
                safe_speed = max(5.0, current_speed * 0.6)  # 急速减速，但不低于5.0

        elif force_avoidance or current_risk >= urgent_risk_threshold:
            # 紧迫局面 - 采用变速或改向+变速的方式避让
            is_avoiding = True
            avoidance_mode = "combined"

            # 计算避碰决策
            safe_speed, safe_course_deg = collision_avoider.find_safe_velocity(own_state, target_ships)

            # 根据会遇类型调整避让策略
            if encounter_type == "head_on":
                # 对遇局面，向右转向避让（国际海上避碰规则第14条）
                current_heading = np.rad2deg(own_state['psi'])
                # 增大转向角度，确保明显的避让动作
                safe_course_deg = (current_heading + 40) % 360  # 向右转40度
                safe_speed = current_speed * 0.8  # 减速20%

            elif encounter_type == "crossing":
                # 交叉相遇，根据国际海上避碰规则第15条和第17条处理
                current_heading = np.rad2deg(own_state['psi'])

                if target_on_starboard:
                    # 目标船在本船右舷，本船为让路船，应向右转向避让
                    # 增大转向角度，确保明显的避让动作
                    safe_course_deg = (current_heading + 60) % 360  # 向右转60度
                    safe_speed = current_speed * 0.75  # 减速25%

                    if t % 20 == 0:
                        print("交叉相遇：目标船在右舷，本船为让路船，向右转向避让")
                else:
                    # 目标船在本船左舷，本船为直航船，应保持航向和航速
                    # 但在紧急情况下，仍需采取避让行动
                    if current_risk > 0.7:  # 风险较高时
                        safe_course_deg = (current_heading + 30) % 360  # 小幅向右转30度
                        safe_speed = current_speed * 0.9  # 轻微减速10%

                        if t % 20 == 0:
                            print("交叉相遇：目标船在左舷，但风险较高，采取避让行动")
                    else:
                        # 风险较低，保持航向和航速
                        safe_course_deg = current_heading
                        safe_speed = current_speed

                        if t % 20 == 0:
                            print("交叉相遇：目标船在左舷，本船为直航船，保持航向和航速")

        elif force_avoidance or current_risk >= general_risk_threshold:
            # 一般风险 - 优先采取改向避让
            is_avoiding = True
            avoidance_mode = "course_change"

            # 计算避碰决策，但只改变航向，保持速度
            safe_speed, safe_course_deg = collision_avoider.find_safe_velocity(own_state, target_ships)
            safe_speed = current_speed  # 保持当前速度

            # 根据会遇类型调整避让策略
            if encounter_type == "head_on":
                # 对遇局面，向右转向避让
                current_heading = np.rad2deg(own_state['psi'])
                # 增大转向角度，确保明显的避让动作
                safe_course_deg = (current_heading + 30) % 360  # 向右转30度

            elif encounter_type == "crossing":
                # 交叉相遇，根据国际海上避碰规则第15条和第17条处理
                current_heading = np.rad2deg(own_state['psi'])

                if target_on_starboard:
                    # 目标船在本船右舷，本船为让路船，应向右转向避让
                    # 增大转向角度，确保明显的避让动作
                    safe_course_deg = (current_heading + 45) % 360  # 向右转45度

                    if t % 20 == 0:
                        print("一般风险交叉相遇：目标船在右舷，本船为让路船，向右转向避让")
                else:
                    # 目标船在本船左舷，本船为直航船，应保持航向和航速
                    # 但在一定风险下，可以采取小幅避让行动
                    if current_risk > 0.5:  # 风险中等时
                        safe_course_deg = (current_heading + 20) % 360  # 小幅向右转20度

                        if t % 20 == 0:
                            print("一般风险交叉相遇：目标船在左舷，采取小幅避让行动")
                    else:
                        # 风险较低，保持航向
                        safe_course_deg = current_heading

                        if t % 20 == 0:
                            print("一般风险交叉相遇：目标船在左舷，本船为直航船，保持航向")

        elif current_risk < safe_threshold and is_avoiding:
            # 风险已降低到安全水平，恢复原航向和航速
            is_avoiding = False
            avoidance_mode = "none"
            safe_course_deg = target_course
            safe_speed = target_speed

        else:
            # 保持当前航向和航速
            safe_course_deg = np.rad2deg(own_state['psi'])
            safe_speed = current_speed

        # 计算控制输出
        current_heading = np.rad2deg(own_state['psi'])
        current_speed = np.sqrt(own_state['u']**2 + own_state['v']**2)

        # 打印当前角速度，用于调试
        if t % 20 == 0:
            print(f"当前角速度: {np.rad2deg(own_state['r']):.3f}°/s")
            print(f"当前横向速度: {own_state['v']:.3f} m/s")

        # 检测是否处于旋回状态
        # 对于新的船舶模型，最大角速度约为0.9°/s，因此设置阈值为0.7°/s
        is_spinning = abs(np.rad2deg(own_state['r'])) > 0.7  # 角速度大于0.7°/s认为在旋回

        # 检测横向速度是否过大
        is_lateral_speed_high = abs(own_state['v']) > 0.1 * own_state['u']  # 横向速度超过纵向速度的10%认为较大

        if (is_spinning or is_lateral_speed_high) and t % 20 == 0:
            print(f"警告：检测到船舶状态异常！角速度: {np.rad2deg(own_state['r']):.3f}°/s, 横向速度: {own_state['v']:.3f} m/s")

        if is_avoiding:
            # 避让状态下，根据避让模式采取不同的控制策略

            # 对于新的船舶模型，旋回不再是问题，因为已经调整了模型参数
            # 但仍然保留检测逻辑，以便在极端情况下采取措施
            if is_spinning:
                # 紧急措施：减小舵角，保持当前速度
                heading_command = 0.0  # 暂时不改变航向
                speed_command = speed_controller.compute(
                    current_speed, current_speed, dt)  # 保持当前速度

                if t % 20 == 0:
                    print("检测到较大角速度，暂时停止转向")
            else:
                # 正常避让控制
                if avoidance_mode == "course_change":
                    # 仅改变航向，保持速度
                    heading_command = heading_controller.compute(
                        safe_course_deg, current_heading, dt)

                    # 保持当前速度，但略微增加以提高稳定性
                    speed_command = speed_controller.compute(
                        current_speed * 1.05, current_speed, dt)

                elif avoidance_mode == "speed_change":
                    # 仅改变速度，保持航向
                    heading_command = heading_controller.compute(
                        current_heading, current_heading, dt)

                    # 调整速度
                    speed_command = speed_controller.compute(
                        safe_speed, current_speed, dt)

                else:  # "combined" 或 "emergency"
                    # 同时改变航向和速度
                    heading_command = heading_controller.compute(
                        safe_course_deg, current_heading, dt)

                    speed_command = speed_controller.compute(
                        safe_speed, current_speed, dt)

                    # 紧急情况下，但要注意不要过度控制
                    if avoidance_mode == "emergency":
                        heading_command *= 1.2  # 增加20%的舵角
                        speed_command *= 1.1    # 增加10%的推进力/制动力
        else:
            # 非避让状态下，使用目标航向和速度
            if abs(current_heading - target_course) > 5:
                heading_command = heading_controller.compute(
                    target_course, current_heading, dt)
            else:
                heading_command = 0.0  # 保持航向

            if abs(current_speed - target_speed) > 0.5:
                speed_command = speed_controller.compute(
                    target_speed, current_speed, dt)
            else:
                speed_command = 0.0  # 保持航速

        # 限制舵角变化率，防止突然转向导致旋回
        max_rudder_change = 2.0  # 每步最大舵角变化（度）
        if abs(heading_command) > max_rudder_change:
            if heading_command > 0:
                heading_command = max_rudder_change
            else:
                heading_command = -max_rudder_change

        # 控制执行
        # 限制舵角在±35度范围内
        delta_r = np.clip(heading_command, -35.0, 35.0)

        # 将速度控制输出映射到螺旋桨转速
        base_rps = 40  # 基础转速
        n_p = base_rps + speed_command * 0.6  # 增加映射系数以提高响应性
        n_p = np.clip(n_p, 0, 100)  # 确保转速在有效范围内

        # 更新船舶状态
        X, Y, N = own_ship.calculate_forces(np.deg2rad(delta_r), n_p)
        own_ship.update_state(X, Y, N, dt)

        # 不再需要额外限制角速度和横向速度
        # 因为已经在MMG模型的update_state方法中实现了更精确的限制
        # 这样可以避免重复限制导致的问题

        # 更新可视化
        if t % 10 == 0:  # 每10步更新一次显示
            visualizer.clear()

            # 绘制船舶和轨迹
            visualizer.draw_ship(own_state, 'own_ship', color='blue')
            visualizer.draw_trajectory(own_trajectory, 'own_ship', color='blue')

            # 绘制本船安全域
            own_safety_radius = risk_assessor.calculate_ship_domain(
                'own_ship', own_state.get('length', 100))
            # 使用椭圆形状绘制本船安全域
            a = own_safety_radius * 2.0  # 长轴
            b = own_safety_radius * 1.5  # 短轴
            ellipse = Ellipse((own_state['x'], own_state['y']),
                            width=a, height=b,
                            angle=np.rad2deg(own_state['psi']),
                            color='blue', alpha=0.2, fill=True)
            visualizer.ax.add_patch(ellipse)

            # 添加安全域边界线
            ellipse_border = Ellipse((own_state['x'], own_state['y']),
                                   width=a, height=b,
                                   angle=np.rad2deg(own_state['psi']),
                                   edgecolor='blue', facecolor='none',
                                   linestyle='--', linewidth=2)
            visualizer.ax.add_patch(ellipse_border)

            # 添加标签
            visualizer.ax.text(own_state['x'], own_state['y'] + b/2 + 50,
                            f"本船安全域 (半径: {own_safety_radius:.0f}m)",
                            color='blue', fontsize=10,
                            horizontalalignment='center')

            for target_ship in target_ships:
                visualizer.draw_ship(target_ship, target_ship['id'],
                                   color='red', is_target=True)
                visualizer.draw_trajectory(
                    target_trajectories[target_ship['id']],
                    target_ship['id'], color='red')

                # 绘制目标船安全域
                target_safety_radius = risk_assessor.calculate_ship_domain(
                    target_ship['type'], target_ship['length'])
                # 为目标船绘制圆形安全域
                safety_circle = Circle(
                    (target_ship['x'], target_ship['y']),
                    target_safety_radius,
                    color='red', alpha=0.1, fill=True
                )
                visualizer.ax.add_patch(safety_circle)

            # 计算当前速度向量
            current_vel = np.array([
                own_state['u'] * np.cos(own_state['psi']),
                own_state['u'] * np.sin(own_state['psi'])
            ])

            # 计算安全速度向量（如果处于避让状态）
            if is_avoiding:
                safe_vel = np.array([
                    safe_speed * np.cos(np.deg2rad(safe_course_deg)),
                    safe_speed * np.sin(np.deg2rad(safe_course_deg))
                ])
            else:
                # 如果不处于避让状态，使用当前速度作为安全速度
                safe_vel = current_vel

            # 无论是否处于避让状态，都显示速度障碍
            # 为每个目标船计算并显示速度障碍
            if target_ships:
                # 只为最危险的目标船显示速度障碍
                most_dangerous_idx = risk_result.get('most_dangerous_ship', 0)
                if isinstance(most_dangerous_idx, int) and most_dangerous_idx < len(target_ships):
                    target_ship = target_ships[most_dangerous_idx]

                    # 计算速度障碍
                    vo_apex, left_bound, right_bound = collision_avoider.calculate_velocity_obstacle(
                        own_state, target_ship)

                    # 绘制速度障碍
                    visualizer.draw_velocity_obstacle(
                        vo_apex=vo_apex,
                        left_bound=left_bound,
                        right_bound=right_bound,
                        own_vel=current_vel,
                        safe_vel=safe_vel if is_avoiding else None,  # 只在避让状态下显示安全速度
                        max_speed=collision_avoider.max_speed
                    )

            # 绘制当前速度向量
            visualizer.draw_velocity_vector(
                (own_state['x'], own_state['y']),
                current_vel,
                color='blue',
                label='当前速度'
            )

            # 如果处于避让状态，绘制避让速度向量
            if is_avoiding:
                visualizer.draw_velocity_vector(
                    (own_state['x'], own_state['y']),
                    safe_vel,
                    color='green',
                    label='避让速度'
                )

            # 显示风险指标
            visualizer.draw_risk_indicator(
                (own_state['x'] - 200, own_state['y'] - 200),
                risk_result['highest_risk'])

            # 显示控制信息
            control_outputs = {
                'delta_r': delta_r,
                'n_p': n_p,
                'avoidance_mode': avoidance_mode,  # 添加避让模式信息
                'encounter_type': encounter_type,  # 添加会遇类型信息
                'is_give_way': is_give_way,        # 添加让路船信息
                'target_on_starboard': target_on_starboard  # 添加目标船位置信息
            }

            # 将风险指数添加到目标船舶状态中
            for i, target_ship in enumerate(target_ships):
                target_ship['risk_index'] = risk_result['individual_risks'][i]

            # 显示船舶信息和避让状态
            visualizer.draw_info_text(
                own_state,
                target_ships,
                control_outputs)

            visualizer.add_legend()
            visualizer.update()

        # 打印进度和详细的避让决策信息
        if t % 20 == 0:  # 增加打印频率，每20步打印一次
            print(f"\n===== 仿真进度: {t/time_steps*100:.1f}% =====")
            print(f"当前位置: ({own_state['x']:.1f}, {own_state['y']:.1f})")
            print(f"当前航向: {np.rad2deg(own_state['psi']):.1f}°")
            print(f"当前速度: {np.sqrt(own_state['u']**2 + own_state['v']**2):.1f} m/s")
            print(f"碰撞风险: {risk_result['highest_risk']:.3f}")

            # 打印避让决策信息
            print(f"避让状态: {'是' if is_avoiding else '否'}")
            print(f"避让模式: {avoidance_mode}")
            print(f"会遇类型: {encounter_type}")

            # 打印相对位置信息
            if encounter_type == "crossing":
                print(f"目标船位置: {'右舷' if target_on_starboard else '左舷'}")
                print(f"本船角色: {'让路船' if is_give_way else '直航船'}")

                # 打印避让策略
                if is_give_way:
                    print("避让策略: 向右转向避让（国际海上避碰规则第15条）")
                else:
                    print("避让策略: 保持航向和航速，但在必要时采取避让行动（国际海上避碰规则第17条）")

            # 打印控制输出
            print(f"目标航向: {target_course:.1f}°")
            print(f"安全航向: {safe_course_deg:.1f}°")
            print(f"航向命令: {heading_command:.2f}")
            print(f"舵角: {delta_r:.2f}°")

            # 打印角速度和转向信息
            print(f"角速度: {np.rad2deg(own_state['r']):.3f}°/s")
            print(f"横向速度: {own_state['v']:.3f} m/s")

            # 打印目标船信息
            if target_ships:
                print("\n目标船信息:")
                for i, target in enumerate(target_ships):
                    dx = target['x'] - own_state['x']
                    dy = target['y'] - own_state['y']
                    distance = np.sqrt(dx**2 + dy**2)
                    rel_bearing = np.arctan2(dy, dx) - own_state['psi']
                    rel_bearing_deg = np.rad2deg(rel_bearing) % 360

                    print(f"  目标船 {i+1}:")
                    print(f"    距离: {distance:.1f} m")
                    print(f"    相对方位: {rel_bearing_deg:.1f}°")
                    risk_index = target.get('risk_index', 0)
                    if isinstance(risk_index, (int, float)):
                        print(f"    风险指数: {risk_index:.3f}")
                    else:
                        print(f"    风险指数: {risk_index}")

            print("---")

    # 保存最终结果
    visualizer.save_figure('simulation_result.png')

    # 确保正确关闭可视化器
    visualizer.close()

    # 强制清理matplotlib资源
    plt.close('all')

    print("仿真完成！")
    return own_trajectory, target_trajectories, risk_history

if __name__ == "__main__":
    try:
        run_simulation()
    except Exception as e:
        print(f"程序异常: {str(e)}")
    finally:
        # 确保程序退出前清理所有资源
        plt.close('all')
        print("程序已退出")