# -*- coding: utf-8 -*-
"""
性能分析模块
"""
import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List
from datetime import datetime

class PerformanceAnalyzer:
    def __init__(self, results_dir: str):
        """初始化性能分析器
        
        Args:
            results_dir (str): 结果目录
        """
        self.results_dir = results_dir
        self.simulations = {}
        self._load_simulation_results()
        
    def _load_simulation_results(self):
        """加载所有仿真结果"""
        for sim_id in os.listdir(self.results_dir):
            sim_path = os.path.join(self.results_dir, sim_id)
            if os.path.isdir(sim_path):
                try:
                    # 加载元数据和性能指标
                    with open(os.path.join(sim_path, 'metadata.json'), 'r') as f:
                        metadata = json.load(f)
                    
                    # 加载时间序列数据
                    time_series = pd.read_csv(os.path.join(sim_path, 'time_series.csv'))
                    
                    # 加载事件记录
                    with open(os.path.join(sim_path, 'events.json'), 'r') as f:
                        events = json.load(f)
                    
                    self.simulations[sim_id] = {
                        'metadata': metadata,
                        'time_series': time_series,
                        'events': events
                    }
                except Exception as e:
                    print(f"Error loading simulation {sim_id}: {e}")
    
    def analyze_safety_performance(self) -> Dict:
        """分析安全性能
        
        Returns:
            Dict: 安全性能分析结果
        """
        safety_metrics = []
        
        for sim_id, data in self.simulations.items():
            metrics = data['metadata']['performance_metrics']
            safety_metrics.append({
                'simulation_id': sim_id,
                'min_safety_distance': metrics['min_safety_distance'],
                'max_risk': metrics['risk_statistics']['max_risk'],
                'time_in_high_risk': metrics['risk_statistics']['time_in_high_risk'],
                'collision_avoided': metrics['collision_avoidance_success']['success']
            })
        
        df = pd.DataFrame(safety_metrics)
        
        return {
            'success_rate': df['collision_avoided'].mean(),
            'average_min_distance': df['min_safety_distance'].mean(),
            'average_max_risk': df['max_risk'].mean(),
            'average_high_risk_time': df['time_in_high_risk'].mean(),
            'distance_std': df['min_safety_distance'].std(),
            'risk_std': df['max_risk'].std()
        }
    
    def analyze_efficiency_performance(self) -> Dict:
        """分析效率性能
        
        Returns:
            Dict: 效率性能分析结果
        """
        efficiency_metrics = []
        
        for sim_id, data in self.simulations.items():
            metrics = data['metadata']['performance_metrics']
            control_effort = metrics['control_effort']
            
            efficiency_metrics.append({
                'simulation_id': sim_id,
                'path_deviation': metrics['path_deviation'],
                'total_rudder_change': control_effort['total_rudder_change'],
                'total_speed_change': control_effort['total_speed_change'],
                'simulation_duration': metrics['simulation_duration']
            })
        
        df = pd.DataFrame(efficiency_metrics)
        
        return {
            'average_path_deviation': df['path_deviation'].mean(),
            'average_rudder_effort': df['total_rudder_change'].mean(),
            'average_speed_effort': df['total_speed_change'].mean(),
            'average_duration': df['simulation_duration'].mean(),
            'path_deviation_std': df['path_deviation'].std(),
            'duration_std': df['simulation_duration'].std()
        }
    
    def generate_performance_report(self, output_file: str = 'performance_report.html'):
        """生成性能报告
        
        Args:
            output_file (str): 输出文件名
        """
        safety_performance = self.analyze_safety_performance()
        efficiency_performance = self.analyze_efficiency_performance()
        
        # 生成HTML报告
        html_content = f"""
        <html>
        <head>
            <title>避碰性能分析报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .metric-group {{ margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <h1>避碰性能分析报告</h1>
            <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>分析的仿真数量: {len(self.simulations)}</p>
            
            <div class="metric-group">
                <h2>安全性能指标</h2>
                <table>
                    <tr><th>指标</th><th>值</th></tr>
                    <tr><td>避碰成功率</td><td>{safety_performance['success_rate']:.2%}</td></tr>
                    <tr><td>平均最小安全距离</td><td>{safety_performance['average_min_distance']:.2f} m</td></tr>
                    <tr><td>平均最大风险值</td><td>{safety_performance['average_max_risk']:.2f}</td></tr>
                    <tr><td>高风险暴露时间比例</td><td>{safety_performance['average_high_risk_time']:.2%}</td></tr>
                </table>
            </div>
            
            <div class="metric-group">
                <h2>效率性能指标</h2>
                <table>
                    <tr><th>指标</th><th>值</th></tr>
                    <tr><td>平均航路偏离度</td><td>{efficiency_performance['average_path_deviation']:.2f}</td></tr>
                    <tr><td>平均舵机使用量</td><td>{efficiency_performance['average_rudder_effort']:.2f}</td></tr>
                    <tr><td>平均速度变化量</td><td>{efficiency_performance['average_speed_effort']:.2f}</td></tr>
                    <tr><td>平均避碰时间</td><td>{efficiency_performance['average_duration']:.2f} s</td></tr>
                </table>
            </div>
        </body>
        </html>
        """
        
        with open(output_file, 'w') as f:
            f.write(html_content)
            
        print(f"Performance report generated: {output_file}")
    
    def plot_trajectory_analysis(self, sim_id: str = None):
        """绘制轨迹分析图
        
        Args:
            sim_id (str, optional): 特定仿真ID，为None时绘制所有轨迹
        """
        plt.figure(figsize=(12, 8))
        
        if sim_id is not None and sim_id in self.simulations:
            sims_to_plot = {sim_id: self.simulations[sim_id]}
        else:
            sims_to_plot = self.simulations
        
        for sim_id, data in sims_to_plot.items():
            time_series = data['time_series']
            
            # 绘制本船轨迹
            own_ship_x = time_series['own_ship.x']
            own_ship_y = time_series['own_ship.y']
            plt.plot(own_ship_x, own_ship_y, 'b-', label=f'Own Ship ({sim_id})')
            
            # 绘制目标船轨迹
            target_ships = eval(time_series['target_ships'].iloc[0])
            for i, target in enumerate(target_ships):
                target_x = [eval(row)['x'] for row in time_series['target_ships']]
                target_y = [eval(row)['y'] for row in time_series['target_ships']]
                plt.plot(target_x, target_y, 'r--', 
                        label=f'Target Ship {i+1} ({sim_id})')
        
        plt.grid(True)
        plt.xlabel('X (m)')
        plt.ylabel('Y (m)')
        plt.title('避碰轨迹分析')
        plt.legend()
        plt.axis('equal')
        plt.show()
    
    def plot_risk_analysis(self, sim_id: str = None):
        """绘制风险分析图
        
        Args:
            sim_id (str, optional): 特定仿真ID，为None时绘制所有风险曲线
        """
        plt.figure(figsize=(12, 6))
        
        if sim_id is not None and sim_id in self.simulations:
            sims_to_plot = {sim_id: self.simulations[sim_id]}
        else:
            sims_to_plot = self.simulations
        
        for sim_id, data in sims_to_plot.items():
            time_series = data['time_series']
            time = time_series['time']
            risk = [eval(row)['highest_risk'] 
                   for row in time_series['risk_assessment']]
            
            plt.plot(time, risk, label=f'Simulation {sim_id}')
        
        plt.grid(True)
        plt.xlabel('Time (s)')
        plt.ylabel('Risk Index')
        plt.title('碰撞风险分析')
        plt.legend()
        plt.ylim(0, 1)
        plt.show()
    
    def plot_control_analysis(self, sim_id: str = None):
        """绘制控制输入分析图
        
        Args:
            sim_id (str, optional): 特定仿真ID，为None时绘制所有控制曲线
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        if sim_id is not None and sim_id in self.simulations:
            sims_to_plot = {sim_id: self.simulations[sim_id]}
        else:
            sims_to_plot = self.simulations
        
        for sim_id, data in sims_to_plot.items():
            time_series = data['time_series']
            time = time_series['time']
            
            # 绘制舵角变化
            rudder = [eval(row)['rudder_angle'] 
                     for row in time_series['control_inputs']]
            ax1.plot(time, rudder, label=f'Simulation {sim_id}')
            
            # 绘制速度变化
            speed = [eval(row)['propeller_rps'] 
                    for row in time_series['control_inputs']]
            ax2.plot(time, speed, label=f'Simulation {sim_id}')
        
        ax1.grid(True)
        ax1.set_xlabel('Time (s)')
        ax1.set_ylabel('Rudder Angle (deg)')
        ax1.set_title('舵角控制分析')
        ax1.legend()
        
        ax2.grid(True)
        ax2.set_xlabel('Time (s)')
        ax2.set_ylabel('Propeller RPS')
        ax2.set_title('速度控制分析')
        ax2.legend()
        
        plt.tight_layout()
        plt.show()