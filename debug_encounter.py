# -*- coding: utf-8 -*-
"""
调试会遇类型判断和风险评估
"""
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'ship_collision_avoidance'))

from ship_collision_avoidance.collision_risk.risk_assessment import CollisionRiskAssessment
from ship_collision_avoidance.config import SHIP_CONFIG

def test_encounter_detection():
    """测试会遇类型检测"""
    risk_assessor = CollisionRiskAssessment()

    # 创建对遇场景
    print("=== 测试对遇场景 ===")

    # 本船状态
    own_ship = {
        'x': 0.0,
        'y': 0.0,
        'psi': np.deg2rad(30),  # 30度航向
        'u': 7.0,
        'v': 0.0,
        'r': 0.0,
        'length': SHIP_CONFIG['L'],
        'width': SHIP_CONFIG['B'],
        'type': 'cargo'
    }

    # 右舷来船，对遇状态
    target_ship = {
        'id': 'Target1',
        'x': 800.0,   # 在本船右前方
        'y': 500.0,
        'psi': np.deg2rad(225),  # 225度航向，朝本船方向
        'u': 6.0,
        'v': 0.0,
        'r': 0.0,
        'length': 150.0,
        'width': 20.0,
        'type': 'cargo'
    }

    print(f"本船位置: ({own_ship['x']}, {own_ship['y']})")
    print(f"本船航向: {np.rad2deg(own_ship['psi']):.1f}°")
    print(f"本船速度: {own_ship['u']} m/s")

    print(f"目标船位置: ({target_ship['x']}, {target_ship['y']})")
    print(f"目标船航向: {np.rad2deg(target_ship['psi']):.1f}°")
    print(f"目标船速度: {target_ship['u']} m/s")

    # 计算会遇类型
    encounter_info = risk_assessor.calculate_encounter_situation(own_ship, target_ship)

    print(f"\n会遇分析结果:")
    print(f"会遇类型: {encounter_info['type']}")
    print(f"相对方位: {encounter_info['relative_bearing']:.1f}°")
    print(f"目标船在右舷: {encounter_info['target_on_starboard']}")
    print(f"本船是让路船: {encounter_info['is_give_way']}")

    # 计算DCPA和TCPA
    dcpa, tcpa = risk_assessor.calculate_dcpa_tcpa(own_ship, target_ship)
    print(f"\nDCPA: {dcpa:.1f} m")
    print(f"TCPA: {tcpa:.1f} s")

    # 计算风险指数
    try:
        risk_result = risk_assessor.calculate_risk_index(own_ship, target_ship)
        print(f"风险结果类型: {type(risk_result)}")
        print(f"风险结果值: {risk_result}")

        if isinstance(risk_result, dict):
            risk_index = risk_result.get('risk_index', 0)
            if isinstance(risk_index, (int, float)):
                print(f"风险指数: {risk_index:.3f}")
            else:
                print(f"风险指数: {risk_index}")
        else:
            risk_index = risk_result
            if isinstance(risk_index, (int, float)):
                print(f"风险指数: {risk_index:.3f}")
            else:
                print(f"风险指数: {risk_index}")
    except Exception as e:
        print(f"计算风险指数时出错: {e}")
        risk_index = 0.0

    # 计算距离
    distance = np.sqrt((target_ship['x'] - own_ship['x'])**2 +
                      (target_ship['y'] - own_ship['y'])**2)
    print(f"当前距离: {distance:.1f} m")

    # 分析为什么风险指数可能较低
    print(f"\n风险分析:")
    print(f"安全距离阈值: {risk_assessor.safe_distance} m")
    print(f"DCPA阈值: {risk_assessor.min_dcpa} m")
    print(f"TCPA阈值: {risk_assessor.min_tcpa} s")

    # 计算各个风险分量
    distance_risk = max(0, 1 - distance / risk_assessor.safe_distance)
    dcpa_risk = max(0, 1 - dcpa / risk_assessor.min_dcpa)

    if tcpa < 0 or tcpa > risk_assessor.max_tcpa:
        tcpa_risk = 0
    else:
        tcpa_risk = max(0, 1 - abs(tcpa - risk_assessor.min_tcpa) /
                      (risk_assessor.max_tcpa - risk_assessor.min_tcpa))

    print(f"距离风险分量: {distance_risk:.3f}")
    print(f"DCPA风险分量: {dcpa_risk:.3f}")
    print(f"TCPA风险分量: {tcpa_risk:.3f}")

    # 测试不同距离下的风险变化
    print(f"\n=== 测试不同距离下的风险变化 ===")
    test_distances = [1500, 1000, 800, 600, 400, 200]

    for test_dist in test_distances:
        # 调整目标船位置，保持相对方位不变
        angle = np.arctan2(target_ship['y'] - own_ship['y'],
                          target_ship['x'] - own_ship['x'])

        test_target = target_ship.copy()
        test_target['x'] = own_ship['x'] + test_dist * np.cos(angle)
        test_target['y'] = own_ship['y'] + test_dist * np.sin(angle)

        test_risk = risk_assessor.calculate_risk_index(own_ship, test_target)
        test_dcpa, test_tcpa = risk_assessor.calculate_dcpa_tcpa(own_ship, test_target)

        print(f"距离 {test_dist}m: 风险={test_risk:.3f}, DCPA={test_dcpa:.1f}m, TCPA={test_tcpa:.1f}s")

if __name__ == "__main__":
    test_encounter_detection()
