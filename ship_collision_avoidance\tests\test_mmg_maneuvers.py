# -*- coding: utf-8 -*-
"""
MMG模型操纵性能测试
包括旋回圈和Z型操舵实验
"""
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import Circle
import matplotlib.gridspec as gridspec
from ship_collision_avoidance.models.mmg_model import MMGModel
from ship_collision_avoidance.config import SHIP_CONFIG
import platform

# 配置中文字体
def setup_chinese_font():
    """配置中文字体支持"""
    system = platform.system()
    if system == "Windows":
        font_path = "C:/Windows/Fonts/msyh.ttc"  # 微软雅黑
    elif system == "Linux":
        font_path = "/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf"
    elif system == "Darwin":  # macOS
        font_path = "/System/Library/Fonts/PingFang.ttc"
    else:
        return None
    
    try:
        from matplotlib import font_manager
        font_manager.fontManager.addfont(font_path)
        plt.rcParams['font.family'] = ['sans-serif']
        if system == "Windows":
            plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
        elif system == "Linux":
            plt.rcParams['font.sans-serif'] = ['Droid Sans Fallback']
        elif system == "Darwin":
            plt.rcParams['font.sans-serif'] = ['PingFang SC']
        plt.rcParams['axes.unicode_minus'] = False  # 正确显示负号
        return True
    except Exception as e:
        print(f"警告：中文字体配置失败 - {str(e)}")
        return False

def run_turning_circle_test(rudder_angle=35, duration=500, dt=0.1, initial_speed=7.0):
    """
    执行旋回圈测试
    
    Args:
        rudder_angle (float): 舵角（度）
        duration (int): 模拟持续时间步数
        dt (float): 时间步长
        initial_speed (float): 初始速度 (m/s)
        
    Returns:
        dict: 包含测试结果的字典
    """
    # 初始化模型
    model = MMGModel()
    model.u = initial_speed  # 设置初始速度
    
    # 初始化结果存储
    results = {
        'time': np.zeros(duration),
        'x': np.zeros(duration),
        'y': np.zeros(duration),
        'psi': np.zeros(duration),
        'u': np.zeros(duration),
        'v': np.zeros(duration),
        'r': np.zeros(duration),
        'rudder': np.zeros(duration)
    }
    
    # 记录初始状态
    results['x'][0] = model.x
    results['y'][0] = model.y
    results['psi'][0] = model.psi
    results['u'][0] = model.u
    results['v'][0] = model.v
    results['r'][0] = model.r
    
    # 转换舵角到弧度
    rudder_rad = np.deg2rad(rudder_angle)
    
    # 设置螺旋桨转速以维持初始速度
    n_p = 60  # 假设60 RPS可以维持初始速度
    
    # 执行模拟
    for i in range(1, duration):
        # 计算力和力矩
        X, Y, N = model.calculate_forces(rudder_rad, n_p)
        
        # 更新状态
        model.update_state(X, Y, N, dt)
        
        # 记录结果
        results['time'][i] = i * dt
        results['x'][i] = model.x
        results['y'][i] = model.y
        results['psi'][i] = model.psi
        results['u'][i] = model.u
        results['v'][i] = model.v
        results['r'][i] = model.r
        results['rudder'][i] = rudder_angle
    
    # 计算旋回圈性能指标
    # 找到船舶转向90度的点
    psi_initial = results['psi'][0]
    psi_90 = (psi_initial + np.pi/2) % (2*np.pi)
    psi_180 = (psi_initial + np.pi) % (2*np.pi)
    psi_270 = (psi_initial + 3*np.pi/2) % (2*np.pi)
    
    # 找到最接近这些角度的索引
    idx_90 = np.argmin(np.abs(results['psi'] - psi_90))
    idx_180 = np.argmin(np.abs(results['psi'] - psi_180))
    idx_270 = np.argmin(np.abs(results['psi'] - psi_270))
    
    # 计算战术直径和前进距离
    if idx_180 > 0:
        tactical_diameter = abs(results['y'][idx_180] - results['y'][0])
        advance = results['x'][idx_90] - results['x'][0]
        transfer = abs(results['y'][idx_90] - results['y'][0])
    else:
        tactical_diameter = None
        advance = None
        transfer = None
    
    # 添加性能指标到结果
    results['tactical_diameter'] = tactical_diameter
    results['advance'] = advance
    results['transfer'] = transfer
    
    return results

def run_zigzag_test(rudder_angle=20, duration=500, dt=0.1, initial_speed=7.0):
    """
    执行Z型操舵测试
    
    Args:
        rudder_angle (float): 舵角幅度（度）
        duration (int): 模拟持续时间步数
        dt (float): 时间步长
        initial_speed (float): 初始速度 (m/s)
        
    Returns:
        dict: 包含测试结果的字典
    """
    # 初始化模型
    model = MMGModel()
    model.u = initial_speed  # 设置初始速度
    
    # 初始化结果存储
    results = {
        'time': np.zeros(duration),
        'x': np.zeros(duration),
        'y': np.zeros(duration),
        'psi': np.zeros(duration),
        'u': np.zeros(duration),
        'v': np.zeros(duration),
        'r': np.zeros(duration),
        'rudder': np.zeros(duration)
    }
    
    # 记录初始状态
    results['x'][0] = model.x
    results['y'][0] = model.y
    results['psi'][0] = model.psi
    results['u'][0] = model.u
    results['v'][0] = model.v
    results['r'][0] = model.r
    
    # 设置螺旋桨转速以维持初始速度
    n_p = 60  # 假设60 RPS可以维持初始速度
    
    # Z型操舵逻辑变量
    target_heading = np.rad2deg(model.psi)  # 初始目标航向（度）
    current_rudder = rudder_angle  # 初始舵角（度）
    heading_threshold = rudder_angle  # 航向偏差阈值（度）
    
    # 执行模拟
    for i in range(1, duration):
        # 获取当前航向（度）
        current_heading = np.rad2deg(model.psi)
        
        # Z型操舵控制逻辑
        if abs(current_heading - target_heading) >= heading_threshold:
            # 如果航向偏差超过阈值，切换舵角方向
            current_rudder = -current_rudder
            # 更新目标航向
            target_heading = current_heading
        
        # 转换舵角到弧度
        rudder_rad = np.deg2rad(current_rudder)
        
        # 计算力和力矩
        X, Y, N = model.calculate_forces(rudder_rad, n_p)
        
        # 更新状态
        model.update_state(X, Y, N, dt)
        
        # 记录结果
        results['time'][i] = i * dt
        results['x'][i] = model.x
        results['y'][i] = model.y
        results['psi'][i] = model.psi
        results['u'][i] = model.u
        results['v'][i] = model.v
        results['r'][i] = model.r
        results['rudder'][i] = current_rudder
    
    # 计算Z型操舵性能指标
    # 找到第一次和第二次舵角变化的时间
    rudder_changes = np.where(np.diff(results['rudder']) != 0)[0] + 1
    
    if len(rudder_changes) >= 2:
        # 计算首次超越时间和角度
        first_overshoot_idx = rudder_changes[0]
        first_overshoot_time = results['time'][first_overshoot_idx]
        first_overshoot_angle = np.rad2deg(results['psi'][first_overshoot_idx])
        
        # 计算第二次超越时间和角度
        second_overshoot_idx = rudder_changes[1]
        second_overshoot_time = results['time'][second_overshoot_idx]
        second_overshoot_angle = np.rad2deg(results['psi'][second_overshoot_idx])
        
        # 添加性能指标到结果
        results['first_overshoot_time'] = first_overshoot_time
        results['first_overshoot_angle'] = first_overshoot_angle
        results['second_overshoot_time'] = second_overshoot_time
        results['second_overshoot_angle'] = second_overshoot_angle
    else:
        results['first_overshoot_time'] = None
        results['first_overshoot_angle'] = None
        results['second_overshoot_time'] = None
        results['second_overshoot_angle'] = None
    
    return results

def plot_turning_circle_results(results):
    """
    绘制旋回圈测试结果
    
    Args:
        results (dict): 旋回圈测试结果
    """
    setup_chinese_font()
    
    fig = plt.figure(figsize=(15, 10))
    gs = gridspec.GridSpec(2, 3, figure=fig)
    
    # 绘制轨迹
    ax1 = fig.add_subplot(gs[0, :2])
    ax1.plot(results['x'], results['y'], 'b-', linewidth=2)
    ax1.plot(results['x'][0], results['y'][0], 'go', markersize=8, label='起点')
    
    # 标记90°、180°和270°点
    psi_initial = results['psi'][0]
    psi_90 = (psi_initial + np.pi/2) % (2*np.pi)
    psi_180 = (psi_initial + np.pi) % (2*np.pi)
    psi_270 = (psi_initial + 3*np.pi/2) % (2*np.pi)
    
    idx_90 = np.argmin(np.abs(results['psi'] - psi_90))
    idx_180 = np.argmin(np.abs(results['psi'] - psi_180))
    idx_270 = np.argmin(np.abs(results['psi'] - psi_270))
    
    if idx_90 > 0:
        ax1.plot(results['x'][idx_90], results['y'][idx_90], 'ro', markersize=8, label='90°点')
    if idx_180 > 0:
        ax1.plot(results['x'][idx_180], results['y'][idx_180], 'mo', markersize=8, label='180°点')
    if idx_270 > 0:
        ax1.plot(results['x'][idx_270], results['y'][idx_270], 'co', markersize=8, label='270°点')
    
    # 绘制战术直径和前进距离
    if results['tactical_diameter'] is not None:
        # 绘制战术直径
        y_center = (results['y'][0] + results['y'][idx_180]) / 2
        circle = Circle((results['x'][idx_180], y_center), results['tactical_diameter']/2, 
                       fill=False, linestyle='--', color='r', label='战术直径')
        ax1.add_patch(circle)
        
        # 绘制前进距离
        ax1.plot([results['x'][0], results['x'][idx_90]], [results['y'][0], results['y'][0]], 
                'g--', linewidth=1.5, label='前进距离')
        
        # 绘制横移距离
        ax1.plot([results['x'][0], results['x'][0]], [results['y'][0], results['y'][idx_90]], 
                'm--', linewidth=1.5, label='横移距离')
    
    ax1.set_title('旋回圈轨迹')
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.grid(True)
    ax1.axis('equal')
    ax1.legend()
    
    # 绘制航向随时间变化
    ax2 = fig.add_subplot(gs[0, 2])
    ax2.plot(results['time'], np.rad2deg(results['psi']), 'r-', linewidth=2)
    ax2.set_title('航向角变化')
    ax2.set_xlabel('时间 (s)')
    ax2.set_ylabel('航向角 (度)')
    ax2.grid(True)
    
    # 绘制速度随时间变化
    ax3 = fig.add_subplot(gs[1, 0])
    ax3.plot(results['time'], results['u'], 'b-', linewidth=2, label='纵向速度')
    ax3.plot(results['time'], results['v'], 'r-', linewidth=2, label='横向速度')
    ax3.set_title('速度变化')
    ax3.set_xlabel('时间 (s)')
    ax3.set_ylabel('速度 (m/s)')
    ax3.grid(True)
    ax3.legend()
    
    # 绘制角速度随时间变化
    ax4 = fig.add_subplot(gs[1, 1])
    ax4.plot(results['time'], np.rad2deg(results['r']), 'g-', linewidth=2)
    ax4.set_title('角速度变化')
    ax4.set_xlabel('时间 (s)')
    ax4.set_ylabel('角速度 (度/s)')
    ax4.grid(True)
    
    # 绘制舵角随时间变化
    ax5 = fig.add_subplot(gs[1, 2])
    ax5.plot(results['time'], results['rudder'], 'k-', linewidth=2)
    ax5.set_title('舵角变化')
    ax5.set_xlabel('时间 (s)')
    ax5.set_ylabel('舵角 (度)')
    ax5.grid(True)
    
    # 添加性能指标文本
    if results['tactical_diameter'] is not None:
        textstr = '\n'.join((
            f"战术直径: {results['tactical_diameter']:.1f} m",
            f"前进距离: {results['advance']:.1f} m",
            f"横移距离: {results['transfer']:.1f} m"
        ))
        props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
        ax1.text(0.05, 0.95, textstr, transform=ax1.transAxes, fontsize=10,
                verticalalignment='top', bbox=props)
    
    plt.tight_layout()
    plt.savefig('turning_circle_test.png', dpi=300)
    plt.show()

def plot_zigzag_results(results):
    """
    绘制Z型操舵测试结果
    
    Args:
        results (dict): Z型操舵测试结果
    """
    setup_chinese_font()
    
    fig = plt.figure(figsize=(15, 10))
    gs = gridspec.GridSpec(2, 3, figure=fig)
    
    # 绘制轨迹
    ax1 = fig.add_subplot(gs[0, :2])
    ax1.plot(results['x'], results['y'], 'b-', linewidth=2)
    ax1.plot(results['x'][0], results['y'][0], 'go', markersize=8, label='起点')
    ax1.set_title('Z型操舵轨迹')
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.grid(True)
    ax1.legend()
    
    # 绘制航向和舵角随时间变化
    ax2 = fig.add_subplot(gs[0, 2])
    ax2.plot(results['time'], np.rad2deg(results['psi']), 'r-', linewidth=2, label='航向角')
    ax2.plot(results['time'], results['rudder'], 'k--', linewidth=2, label='舵角')
    ax2.set_title('航向角和舵角变化')
    ax2.set_xlabel('时间 (s)')
    ax2.set_ylabel('角度 (度)')
    ax2.grid(True)
    ax2.legend()
    
    # 绘制速度随时间变化
    ax3 = fig.add_subplot(gs[1, 0])
    ax3.plot(results['time'], results['u'], 'b-', linewidth=2, label='纵向速度')
    ax3.plot(results['time'], results['v'], 'r-', linewidth=2, label='横向速度')
    ax3.set_title('速度变化')
    ax3.set_xlabel('时间 (s)')
    ax3.set_ylabel('速度 (m/s)')
    ax3.grid(True)
    ax3.legend()
    
    # 绘制角速度随时间变化
    ax4 = fig.add_subplot(gs[1, 1])
    ax4.plot(results['time'], np.rad2deg(results['r']), 'g-', linewidth=2)
    ax4.set_title('角速度变化')
    ax4.set_xlabel('时间 (s)')
    ax4.set_ylabel('角速度 (度/s)')
    ax4.grid(True)
    
    # 标记超越点
    if results['first_overshoot_time'] is not None:
        # 找到最接近超越时间的索引
        idx1 = np.argmin(np.abs(results['time'] - results['first_overshoot_time']))
        idx2 = np.argmin(np.abs(results['time'] - results['second_overshoot_time']))
        
        # 在航向图上标记超越点
        ax2.plot(results['time'][idx1], np.rad2deg(results['psi'][idx1]), 'ro', markersize=8, label='第一次超越')
        ax2.plot(results['time'][idx2], np.rad2deg(results['psi'][idx2]), 'mo', markersize=8, label='第二次超越')
        ax2.legend()
        
        # 在轨迹图上标记超越点
        ax1.plot(results['x'][idx1], results['y'][idx1], 'ro', markersize=8, label='第一次超越')
        ax1.plot(results['x'][idx2], results['y'][idx2], 'mo', markersize=8, label='第二次超越')
        ax1.legend()
        
        # 添加性能指标文本
        textstr = '\n'.join((
            f"第一次超越时间: {results['first_overshoot_time']:.1f} s",
            f"第一次超越角度: {results['first_overshoot_angle']:.1f}°",
            f"第二次超越时间: {results['second_overshoot_time']:.1f} s",
            f"第二次超越角度: {results['second_overshoot_angle']:.1f}°"
        ))
        props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
        ax2.text(0.05, 0.05, textstr, transform=ax2.transAxes, fontsize=10,
                verticalalignment='bottom', bbox=props)
    
    # 绘制舵角变化点
    rudder_changes = np.where(np.diff(results['rudder']) != 0)[0] + 1
    ax5 = fig.add_subplot(gs[1, 2])
    ax5.plot(results['time'], results['rudder'], 'k-', linewidth=2)
    for idx in rudder_changes:
        ax5.plot(results['time'][idx], results['rudder'][idx], 'ro', markersize=6)
    ax5.set_title('舵角变化')
    ax5.set_xlabel('时间 (s)')
    ax5.set_ylabel('舵角 (度)')
    ax5.grid(True)
    
    plt.tight_layout()
    plt.savefig('zigzag_test.png', dpi=300)
    plt.show()

def main():
    """主函数，运行所有测试"""
    print("开始执行MMG模型操纵性能测试...")
    
    # 执行旋回圈测试
    print("\n执行旋回圈测试...")
    turning_results = run_turning_circle_test(rudder_angle=35, duration=500, dt=0.1, initial_speed=7.0)
    plot_turning_circle_results(turning_results)
    
    # 执行Z型操舵测试
    print("\n执行Z型操舵测试...")
    zigzag_results = run_zigzag_test(rudder_angle=20, duration=500, dt=0.1, initial_speed=7.0)
    plot_zigzag_results(zigzag_results)
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
