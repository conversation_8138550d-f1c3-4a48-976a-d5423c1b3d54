import pytest
import matplotlib.pyplot as plt
import os
from ship_collision_avoidance.visualization.font_config import FontConfig

class TestFontConfig:
    """字体配置测试类"""
    
    @pytest.fixture
    def font_config(self):
        """创建 FontConfig 实例"""
        return FontConfig()
        
    def test_init(self, font_config):
        """测试初始化"""
        assert font_config.system in ['Windows', 'Linux', 'Darwin']
        assert font_config._font_info is not None
        
    def test_get_default_font(self, font_config):
        """测试获取默认字体"""
        default_font = font_config.get_system_default_font()
        assert isinstance(default_font, str)
        assert len(default_font) > 0
        
    def test_apply_font_config(self, font_config):
        """测试应用字体配置"""
        font_config.apply_font_config()
        current_config = font_config.get_current_matplotlib_config()
        assert current_config['family'] == 'sans-serif'
        assert isinstance(current_config['sans-serif'], list)
        assert len(current_config['sans-serif']) > 0
        assert isinstance(current_config['unicode_minus'], bool)
        
    def test_reset_to_default(self, font_config):
        """测试重置为默认配置"""
        # 先修改配置
        plt.rcParams['font.family'] = ['serif']
        plt.rcParams['font.sans-serif'] = ['Arial']
        
        # 重置配置
        font_config.reset_to_default()
        current_config = font_config.get_current_matplotlib_config()
        
        # 验证重置结果
        assert current_config['family'] == 'sans-serif'
        assert isinstance(current_config['sans-serif'], list)
        assert font_config.get_system_default_font() in current_config['sans-serif']
        
    @pytest.mark.skipif(not os.path.exists('C:/Windows/Fonts/msyh.ttc'),
                      reason="微软雅黑字体文件不存在")
    def test_is_cjk_font_windows(self, font_config):
        """测试 Windows 系统下的 CJK 字体检查"""
        if font_config.system == 'Windows':
            # 测试微软雅黑
            assert font_config.is_cjk_font('Microsoft YaHei')
            # 测试宋体
            assert font_config.is_cjk_font('SimSun')
            
    def test_is_cjk_font_non_cjk(self, font_config):
        """测试非 CJK 字体"""
        assert not font_config.is_cjk_font('Arial')
        assert not font_config.is_cjk_font('Times New Roman')
        
    def test_verify_font(self, font_config):
        """测试字体验证"""
        # 验证系统默认字体
        default_font = font_config.get_system_default_font()
        assert font_config.verify_font(default_font)
        
        # 验证不存在的字体
        assert not font_config.verify_font('NonExistentFont') 